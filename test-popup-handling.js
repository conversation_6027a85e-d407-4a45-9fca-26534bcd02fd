#!/usr/bin/env node

/**
 * Test script specifically for popup handling
 * Tests the new popup detection and dismissal functionality
 */

const { chromium } = require('playwright');
const path = require('path');

class PopupTester {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  async initialize() {
    console.log('🚀 Initializing popup tester...');
    
    // Use same configuration as automation
    const mobileViewport = {
      width: 375,
      height: 667
    };

    const desktopUserAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';

    this.browser = await chromium.launch({
      headless: false,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--window-size=395,767',
        '--window-position=100,100'
      ]
    });

    const context = await this.browser.newContext({
      viewport: mobileViewport,
      userAgent: desktopUserAgent
    });

    this.page = await context.newPage();
    console.log('✅ Browser initialized with mobile viewport and desktop user agent');
  }

  async handleTikTokPopups(accountId = 'test') {
    try {
      console.log('🔍 Checking for popups...');

      // Handle "Open App Store?" popup
      const appStorePopup = this.page.locator('text="Open App Store?"').first();
      if (await appStorePopup.isVisible({ timeout: 2000 })) {
        console.log('❌ Found "Open App Store?" popup - dismissing...');
        const cancelButton = this.page.locator('button:has-text("Cancel")').first();
        if (await cancelButton.isVisible()) {
          await cancelButton.click();
          await this.sleep(1000, 2000);
          console.log('✅ Dismissed "Open App Store?" popup');
        }
      } else {
        console.log('✅ No "Open App Store?" popup found');
      }

      // Handle login popup
      const loginPopup = this.page.locator('[data-e2e="login-modal"]').first();
      if (await loginPopup.isVisible({ timeout: 2000 })) {
        console.log('❌ Found login popup - dismissing...');
        const closeButton = this.page.locator('[data-e2e="modal-close-inner-button"]').first();
        if (await closeButton.isVisible()) {
          await closeButton.click();
          await this.sleep(1000, 2000);
          console.log('✅ Dismissed login popup');
        }
      } else {
        console.log('✅ No login popup found');
      }

      // Handle cookie consent
      const cookieConsent = this.page.locator('text="Accept all"').first();
      if (await cookieConsent.isVisible({ timeout: 2000 })) {
        console.log('🍪 Found cookie consent - accepting...');
        await cookieConsent.click();
        await this.sleep(1000, 2000);
        console.log('✅ Accepted cookies');
      } else {
        console.log('✅ No cookie consent found');
      }

      // Check for any other modals
      const modals = await this.page.locator('[role="dialog"]').count();
      if (modals > 0) {
        console.log(`⚠️ Found ${modals} modal(s) on page`);
      } else {
        console.log('✅ No modals detected');
      }

    } catch (error) {
      console.log(`⚠️ Popup handling warning: ${error.message}`);
    }
  }

  async sleep(min, max) {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    return new Promise(resolve => setTimeout(resolve, delay));
  }

  async testTikTokNavigation() {
    console.log('\n🧪 Testing TikTok navigation and popup handling...\n');

    try {
      // Test 1: Navigate to TikTok homepage
      console.log('1️⃣ Testing TikTok homepage...');
      await this.page.goto('https://www.tiktok.com', { waitUntil: 'domcontentloaded', timeout: 30000 });
      await this.sleep(3000, 5000);
      await this.handleTikTokPopups();

      // Test 2: Navigate to a popular profile
      console.log('\n2️⃣ Testing profile page...');
      await this.page.goto('https://www.tiktok.com/@tiktok', { waitUntil: 'domcontentloaded', timeout: 30000 });
      await this.sleep(3000, 5000);
      await this.handleTikTokPopups();

      // Test 3: Try to access followers (this often triggers popups)
      console.log('\n3️⃣ Testing followers access...');
      try {
        const followersButton = this.page.locator('[data-e2e="followers-count"]').first();
        if (await followersButton.isVisible({ timeout: 5000 })) {
          console.log('📊 Found followers button, clicking...');
          await followersButton.click();
          await this.sleep(3000, 5000);
          await this.handleTikTokPopups();
        } else {
          console.log('⚠️ Followers button not found');
        }
      } catch (error) {
        console.log(`⚠️ Followers test error: ${error.message}`);
      }

      // Test 4: Navigate to a video
      console.log('\n4️⃣ Testing video page...');
      try {
        // Try to find and click on a video
        const videoLink = this.page.locator('[data-e2e="user-post-item"] a').first();
        if (await videoLink.isVisible({ timeout: 5000 })) {
          const href = await videoLink.getAttribute('href');
          if (href) {
            console.log('🎥 Found video, navigating...');
            await this.page.goto(`https://www.tiktok.com${href}`, { waitUntil: 'domcontentloaded', timeout: 30000 });
            await this.sleep(3000, 5000);
            await this.handleTikTokPopups();
          }
        } else {
          console.log('⚠️ No videos found on profile');
        }
      } catch (error) {
        console.log(`⚠️ Video test error: ${error.message}`);
      }

      console.log('\n✅ All navigation tests completed!');

    } catch (error) {
      console.error('❌ Navigation test failed:', error.message);
    }
  }

  async testUserAgentComparison() {
    console.log('\n🧪 Testing User Agent comparison...\n');

    // Test with mobile user agent
    console.log('📱 Testing with mobile user agent...');
    await this.page.setUserAgent('Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1');
    
    await this.page.goto('https://www.tiktok.com/@tiktok', { waitUntil: 'domcontentloaded', timeout: 30000 });
    await this.sleep(3000, 5000);
    
    const mobilePopups = await this.page.locator('text="Open App Store?"').count();
    console.log(`Mobile UA - App Store popups found: ${mobilePopups}`);

    // Test with desktop user agent
    console.log('\n💻 Testing with desktop user agent...');
    await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
    
    await this.page.goto('https://www.tiktok.com/@tiktok', { waitUntil: 'domcontentloaded', timeout: 30000 });
    await this.sleep(3000, 5000);
    
    const desktopPopups = await this.page.locator('text="Open App Store?"').count();
    console.log(`Desktop UA - App Store popups found: ${desktopPopups}`);

    if (mobilePopups > desktopPopups) {
      console.log('✅ Desktop user agent reduces popup frequency');
    } else {
      console.log('⚠️ No significant difference in popup frequency');
    }
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up...');
    if (this.browser) {
      await this.browser.close();
    }
    console.log('✅ Cleanup completed');
  }

  async run() {
    try {
      await this.initialize();
      
      console.log('\n🧪 Starting popup handling tests...\n');
      console.log('⏳ This will take about 2-3 minutes...\n');
      
      await this.testTikTokNavigation();
      await this.testUserAgentComparison();
      
      console.log('\n🎉 All tests completed successfully!');
      console.log('\n📋 Summary:');
      console.log('- Desktop user agent should reduce "Open App Store?" popups');
      console.log('- Popup handling functions should dismiss any remaining popups');
      console.log('- Mobile viewport maintains TikTok mobile experience');
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
    } finally {
      await this.cleanup();
      process.exit(0);
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  const tester = new PopupTester();
  tester.run().catch(console.error);
}

module.exports = PopupTester;
