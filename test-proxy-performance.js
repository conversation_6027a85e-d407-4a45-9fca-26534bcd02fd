#!/usr/bin/env node

/**
 * Proxy Performance Comparison Test
 * So sánh tốc độ của các lo<PERSON>i proxy khác nhau
 */

const { chromium } = require('playwright');
const ProxyService = require('./backend/src/services/ProxyService');

class ProxyPerformanceTester {
  constructor() {
    this.proxyService = new ProxyService();
    this.results = [];
  }

  /**
   * Test proxy performance
   */
  async testProxyPerformance(proxyConfig, testName) {
    console.log(`\n🧪 Testing ${testName}...`);
    
    const results = {
      name: testName,
      proxyType: proxyConfig?.type || 'No proxy',
      connectionTime: 0,
      pageLoadTime: 0,
      totalTime: 0,
      success: false,
      error: null,
      ipAddress: null
    };

    let browser = null;
    let startTime = Date.now();

    try {
      // Create browser with proxy
      const browserOptions = {
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      };

      if (proxyConfig) {
        if (proxyConfig.type === 'SOCKS5' && proxyConfig.username) {
          // Use bridge for SOCKS5 with auth
          const proxyConfigResult = await this.proxyService.createProxyConfigWithBridge(proxyConfig, 'test');
          browserOptions.proxy = proxyConfigResult;
        } else {
          // Use direct proxy config
          browserOptions.proxy = this.proxyService.createProxyConfig(proxyConfig);
        }
      }

      // Measure connection time
      const connectionStart = Date.now();
      browser = await chromium.launch(browserOptions);
      const context = await browser.newContext();
      const page = await context.newPage();
      results.connectionTime = Date.now() - connectionStart;

      // Measure page load time
      const pageLoadStart = Date.now();
      
      // Test with a simple, fast-loading page
      await page.goto('https://httpbin.org/ip', { 
        waitUntil: 'domcontentloaded', 
        timeout: 30000 
      });
      
      results.pageLoadTime = Date.now() - pageLoadStart;

      // Get IP address to verify proxy
      try {
        const content = await page.textContent('body');
        const ipData = JSON.parse(content);
        results.ipAddress = ipData.origin;
      } catch (error) {
        console.log('⚠️ Could not parse IP response');
      }

      results.totalTime = Date.now() - startTime;
      results.success = true;

      console.log(`✅ ${testName} completed:`);
      console.log(`   Connection: ${results.connectionTime}ms`);
      console.log(`   Page Load: ${results.pageLoadTime}ms`);
      console.log(`   Total: ${results.totalTime}ms`);
      console.log(`   IP: ${results.ipAddress}`);

    } catch (error) {
      results.error = error.message;
      results.totalTime = Date.now() - startTime;
      console.log(`❌ ${testName} failed: ${error.message}`);
    } finally {
      if (browser) {
        await browser.close();
      }
      
      // Cleanup SOCKS5 bridge if used
      if (proxyConfig?.type === 'SOCKS5' && proxyConfig.username) {
        await this.proxyService.stopSocks5Bridge('test');
      }
    }

    this.results.push(results);
    return results;
  }

  /**
   * Run comprehensive proxy performance tests
   */
  async runTests() {
    console.log('🚀 Starting Proxy Performance Tests...\n');

    // Test 1: No proxy (baseline)
    await this.testProxyPerformance(null, 'No Proxy (Baseline)');

    // Test 2: HTTP Proxy (if you have one)
    // Uncomment and configure if you have HTTP proxy
    /*
    await this.testProxyPerformance({
      type: 'HTTP',
      host: 'your-http-proxy.com',
      port: 8080,
      username: 'user',
      password: 'pass'
    }, 'HTTP Proxy');
    */

    // Test 3: SOCKS5 without auth (if you have one)
    // Uncomment and configure if you have SOCKS5 without auth
    /*
    await this.testProxyPerformance({
      type: 'SOCKS5',
      host: 'your-socks5-proxy.com',
      port: 1080
    }, 'SOCKS5 (No Auth)');
    */

    // Test 4: SOCKS5 with auth (current setup)
    // Uncomment and configure with your actual SOCKS5 proxy
    /*
    await this.testProxyPerformance({
      type: 'SOCKS5',
      host: 'your-socks5-proxy.com',
      port: 1080,
      username: 'your-username',
      password: 'your-password'
    }, 'SOCKS5 with Auth (Bridge)');
    */

    // Display results
    this.displayResults();
  }

  /**
   * Display test results in a formatted table
   */
  displayResults() {
    console.log('\n📊 Performance Test Results:\n');
    
    console.log('┌─────────────────────────┬─────────────┬─────────────┬─────────────┬─────────────┐');
    console.log('│ Proxy Type              │ Connection  │ Page Load   │ Total Time  │ Status      │');
    console.log('├─────────────────────────┼─────────────┼─────────────┼─────────────┼─────────────┤');
    
    this.results.forEach(result => {
      const name = result.name.padEnd(23);
      const connection = result.success ? `${result.connectionTime}ms`.padEnd(11) : 'Failed'.padEnd(11);
      const pageLoad = result.success ? `${result.pageLoadTime}ms`.padEnd(11) : 'Failed'.padEnd(11);
      const total = result.success ? `${result.totalTime}ms`.padEnd(11) : 'Failed'.padEnd(11);
      const status = result.success ? '✅ Success'.padEnd(11) : '❌ Failed'.padEnd(11);
      
      console.log(`│ ${name} │ ${connection} │ ${pageLoad} │ ${total} │ ${status} │`);
    });
    
    console.log('└─────────────────────────┴─────────────┴─────────────┴─────────────┴─────────────┘');

    // Performance analysis
    this.analyzeResults();
  }

  /**
   * Analyze results and provide recommendations
   */
  analyzeResults() {
    console.log('\n📈 Performance Analysis:\n');

    const successfulResults = this.results.filter(r => r.success);
    
    if (successfulResults.length === 0) {
      console.log('❌ No successful tests to analyze');
      return;
    }

    // Find fastest and slowest
    const fastest = successfulResults.reduce((prev, current) => 
      prev.totalTime < current.totalTime ? prev : current
    );
    
    const slowest = successfulResults.reduce((prev, current) => 
      prev.totalTime > current.totalTime ? prev : current
    );

    console.log(`🏆 Fastest: ${fastest.name} (${fastest.totalTime}ms)`);
    console.log(`🐌 Slowest: ${slowest.name} (${slowest.totalTime}ms)`);
    
    if (successfulResults.length > 1) {
      const speedDifference = slowest.totalTime - fastest.totalTime;
      const percentageDifference = ((speedDifference / fastest.totalTime) * 100).toFixed(1);
      console.log(`📊 Speed difference: ${speedDifference}ms (${percentageDifference}% slower)`);
    }

    // Recommendations
    console.log('\n💡 Recommendations:\n');
    
    const baseline = successfulResults.find(r => r.name.includes('No Proxy'));
    const socks5Bridge = successfulResults.find(r => r.name.includes('Bridge'));
    
    if (baseline && socks5Bridge) {
      const overhead = socks5Bridge.totalTime - baseline.totalTime;
      const overheadPercent = ((overhead / baseline.totalTime) * 100).toFixed(1);
      
      console.log(`🔍 SOCKS5 Bridge Overhead: +${overhead}ms (+${overheadPercent}%)`);
      
      if (overhead > 1000) {
        console.log('⚠️  High overhead detected! Consider switching to HTTP proxy');
      } else if (overhead > 500) {
        console.log('⚠️  Moderate overhead. HTTP proxy would be faster');
      } else {
        console.log('✅ Acceptable overhead for SOCKS5 bridge');
      }
    }

    console.log('\n🎯 Speed Ranking (fastest to slowest):');
    console.log('1. 🥇 HTTP/HTTPS Proxy (if available)');
    console.log('2. 🥈 SOCKS5 without authentication');
    console.log('3. 🥉 No proxy (direct connection)');
    console.log('4. 🐌 SOCKS5 with authentication (bridge)');

    console.log('\n📋 Action Items:');
    console.log('• Consider switching to HTTP proxy for best performance');
    console.log('• If using SOCKS5, try to find providers without auth requirement');
    console.log('• Monitor bridge performance and restart if needed');
    console.log('• Test with your actual proxy providers for real-world results');
  }

  /**
   * Test SOCKS5 bridge performance specifically
   */
  async testBridgePerformance() {
    console.log('\n🌉 Testing SOCKS5 Bridge Performance...\n');

    // This would require actual SOCKS5 proxy credentials
    console.log('To test bridge performance with your proxy:');
    console.log('1. Edit this script with your SOCKS5 proxy details');
    console.log('2. Uncomment the SOCKS5 test sections');
    console.log('3. Run the test again');
    console.log('\nExample configuration:');
    console.log(`{
  type: 'SOCKS5',
  host: 'your-proxy-host.com',
  port: 1080,
  username: 'your-username',
  password: 'your-password'
}`);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new ProxyPerformanceTester();
  
  tester.runTests()
    .then(() => {
      console.log('\n✅ All tests completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = ProxyPerformanceTester;
