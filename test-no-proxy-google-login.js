#!/usr/bin/env node

/**
 * Test script for "No proxy" + Google login account creation
 * <PERSON><PERSON><PERSON> tra việc tạo tài kho<PERSON>n với No proxy và Google login
 */

const DatabaseManager = require('./backend/src/database/manager');
const WebSocketServer = require('./backend/src/server/websocket');
const AccountController = require('./backend/src/controllers/AccountController');
const TikTokLoginAutomation = require('./backend/src/automation/login');

class NoProxyGoogleLoginTester {
  constructor() {
    this.db = new DatabaseManager();
    this.wsServer = new WebSocketServer(8082); // Different port for testing
    this.accountController = null;
    this.loginAutomation = null;
  }

  async initialize() {
    console.log('🚀 Initializing No Proxy + Google Login tester...');
    
    // Initialize database
    await this.db.initialize();
    console.log('✅ Database initialized');

    // Start WebSocket server
    this.wsServer.start();
    console.log('✅ WebSocket server started on port 8082');

    // Initialize controllers
    this.accountController = new AccountController(this.db, this.wsServer);
    this.loginAutomation = new TikTokLoginAutomation(this.wsServer, this.db);
    console.log('✅ Controllers initialized');
  }

  async testAccountCreation() {
    console.log('\n🧪 Testing account creation with No proxy + Google login...\n');

    // Test data for No proxy + Google login
    const testAccountData = {
      username: null, // No username for Google login
      password: null, // No password for Google login
      useGoogleLogin: true,
      proxy: {
        type: 'No proxy',
        host: null,
        port: null,
        username: null,
        password: null,
        country: null,
        city: null
      },
      ipChecker: 'ip-api'
    };

    console.log('📋 Test account data:');
    console.log(JSON.stringify(testAccountData, null, 2));

    try {
      // Mock WebSocket connection
      const mockWs = {
        send: (data) => {
          const message = JSON.parse(data);
          console.log(`📡 WebSocket message: ${message.type} - ${message.message}`);
        }
      };

      // Test account creation
      console.log('\n🔧 Creating account...');
      await this.accountController.createAccount(mockWs, testAccountData);

      // Verify account was created
      const accounts = await this.db.getAccounts();
      const createdAccount = accounts.find(acc => acc.useGoogleLogin === true && acc.proxy.type === 'No proxy');

      if (createdAccount) {
        console.log('\n✅ Account created successfully!');
        console.log('📋 Created account details:');
        console.log(`   ID: ${createdAccount.id}`);
        console.log(`   Username: ${createdAccount.username || 'Google Login'}`);
        console.log(`   Use Google Login: ${createdAccount.useGoogleLogin}`);
        console.log(`   Proxy Type: ${createdAccount.proxy.type}`);
        console.log(`   Status: ${createdAccount.status}`);
        console.log(`   IP Checker: ${createdAccount.ipChecker}`);

        return createdAccount;
      } else {
        throw new Error('Account was not found in database after creation');
      }

    } catch (error) {
      console.error('❌ Account creation failed:', error.message);
      throw error;
    }
  }

  async testLoginProcess(account) {
    console.log('\n🧪 Testing login process...\n');

    try {
      console.log('🔧 Starting login automation...');
      
      // Mock WebSocket for login
      const mockWs = {
        send: (data) => {
          const message = JSON.parse(data);
          if (message.type === 'log') {
            console.log(`📝 Login log [${message.level}]: ${message.message}`);
          } else {
            console.log(`📡 Login message: ${message.type} - ${message.message}`);
          }
        }
      };

      // Test login (this will open browser for manual login)
      console.log('⚠️  This will open a browser window for manual Google login');
      console.log('⚠️  The browser should open without proxy configuration');
      console.log('⚠️  You can close the browser manually after verifying it opens correctly');
      
      // Start login process
      const loginSuccess = await this.loginAutomation.loginAccount(account.id);
      
      if (loginSuccess) {
        console.log('✅ Login process started successfully');
      } else {
        console.log('⚠️  Login process completed (manual intervention may be required)');
      }

    } catch (error) {
      console.error('❌ Login test failed:', error.message);
      
      // Check if it's a navigation error (expected for manual login)
      if (error.message.includes('Navigation') || error.message.includes('Timeout')) {
        console.log('ℹ️  Navigation error is expected for manual login process');
      } else {
        throw error;
      }
    }
  }

  async testProxyConfiguration() {
    console.log('\n🧪 Testing proxy configuration handling...\n');

    const ProxyService = require('./backend/src/services/ProxyService');
    const proxyService = new ProxyService();

    // Test No proxy configuration
    const noProxyConfig = {
      type: 'No proxy',
      host: null,
      port: null
    };

    try {
      console.log('🔧 Testing createProxyConfig with No proxy...');
      const result = proxyService.createProxyConfig(noProxyConfig);
      
      if (result === null) {
        console.log('✅ createProxyConfig correctly returns null for No proxy');
      } else {
        console.log('❌ createProxyConfig should return null for No proxy, got:', result);
      }

    } catch (error) {
      console.error('❌ createProxyConfig failed for No proxy:', error.message);
    }

    try {
      console.log('🔧 Testing createProxyConfigWithBridge with No proxy...');
      const result = await proxyService.createProxyConfigWithBridge(noProxyConfig, 'test-account');
      
      if (result === null) {
        console.log('✅ createProxyConfigWithBridge correctly returns null for No proxy');
      } else {
        console.log('❌ createProxyConfigWithBridge should return null for No proxy, got:', result);
      }

    } catch (error) {
      console.error('❌ createProxyConfigWithBridge failed for No proxy:', error.message);
    }
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up test environment...');
    
    try {
      // Close any open browsers
      await this.loginAutomation.closeAllBrowsers();
      
      // Stop WebSocket server
      this.wsServer.stop();
      
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.error('⚠️ Cleanup error:', error.message);
    }
  }

  async run() {
    try {
      await this.initialize();
      
      console.log('\n🧪 Starting No Proxy + Google Login tests...\n');
      
      // Test 1: Account creation
      const account = await this.testAccountCreation();
      
      // Test 2: Proxy configuration
      await this.testProxyConfiguration();
      
      // Test 3: Login process (optional - requires manual interaction)
      console.log('\n❓ Do you want to test the login process?');
      console.log('   This will open a browser window for manual verification.');
      console.log('   Press Ctrl+C to skip, or wait 10 seconds to continue...\n');
      
      await new Promise(resolve => setTimeout(resolve, 10000));
      
      await this.testLoginProcess(account);
      
      console.log('\n🎉 All tests completed successfully!');
      console.log('\n📋 Summary:');
      console.log('✅ Account creation with No proxy + Google login works');
      console.log('✅ Proxy configuration handling is correct');
      console.log('✅ Login process can be initiated');
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
    } finally {
      await this.cleanup();
      process.exit(0);
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  const tester = new NoProxyGoogleLoginTester();
  tester.run().catch(console.error);
}

module.exports = NoProxyGoogleLoginTester;
