import React, { useState } from 'react'

function AddAccountModal({ isOpen, onClose, onSubmit, wsClient }) {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    useGoogleLogin: false,
    proxy: {
      type: 'HTTP', // No proxy, SSH, HTTPS, HTTP, Socks5
      host: '',
      port: '',
      username: '',
      password: '',
      country: '',
      city: ''
    },
    ipChecker: 'IP2Location' // IP2Location, ip-api, IPIDEA, IPFoxy, IPinfo
  })

  const [proxyStatus, setProxyStatus] = useState(null) // null, 'testing', 'active', 'inactive'
  const [proxyTestResult, setProxyTestResult] = useState(null) // Store detailed test result
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Proxy type options
  const proxyTypes = [
    { value: 'No proxy', label: 'No proxy (local network)' },
    { value: 'HTTP', label: 'HTTP' },
    { value: 'HTTPS', label: 'HTTPS' },
    { value: 'Socks5', label: 'Socks5' },
    { value: 'SSH', label: 'SSH' }
  ]

  // IP checker options
  const ipCheckers = [
    { value: 'IP2Location', label: 'IP2Location' },
    { value: 'ip-api', label: 'ip-api' },
    { value: 'IPIDEA', label: 'IPIDEA' },
    { value: 'IPFoxy', label: 'IPFoxy' },
    { value: 'IPinfo', label: 'IPinfo' }
  ]

  const handleInputChange = (field, value) => {
    if (field.startsWith('proxy.')) {
      const proxyField = field.split('.')[1]
      setFormData(prev => ({
        ...prev,
        proxy: {
          ...prev.proxy,
          [proxyField]: value
        }
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }))
    }
  }

  const handleTestProxy = async () => {
    const { type, host, port, username, password } = formData.proxy

    if (type === 'No proxy') {
      alert('Không thể test "No proxy" - sẽ sử dụng kết nối local')
      return
    }

    if (!host || !port) {
      alert('Vui lòng nhập đầy đủ thông tin proxy (Host và Port)')
      return
    }

    setProxyStatus('testing')
    setProxyTestResult(null)

    try {
      // Gửi request test proxy đến backend
      wsClient.sendCommand('test_proxy', {
        proxy: {
          type: type,
          host: host.trim(),
          port: parseInt(port),
          username: username.trim() || null,
          password: password.trim() || null
        },
        ipChecker: formData.ipChecker
      })

      // Lắng nghe response
      const handleProxyTestResult = (message) => {
        if (message.type === 'proxy_test_result') {
          setProxyStatus(message.isActive ? 'active' : 'inactive')
          setProxyTestResult(message.result || null)
          wsClient.off('proxy_test_result', handleProxyTestResult)
        }
      }

      wsClient.on('proxy_test_result', handleProxyTestResult)

      // Timeout sau 15 giây
      setTimeout(() => {
        if (proxyStatus === 'testing') {
          setProxyStatus('inactive')
          wsClient.off('proxy_test_result', handleProxyTestResult)
        }
      }, 15000)

    } catch (error) {
      console.error('Error testing proxy:', error)
      setProxyStatus('inactive')
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    // Validation
    if (!formData.useGoogleLogin && (!formData.username || !formData.password)) {
      alert('Vui lòng nhập username và password hoặc chọn đăng nhập bằng Google')
      return
    }

    if (formData.proxy.type !== 'No proxy' && (!formData.proxy.host || !formData.proxy.port)) {
      alert('Vui lòng nhập thông tin proxy')
      return
    }

    setIsSubmitting(true)

    try {
      const accountData = {
        username: formData.useGoogleLogin ? null : formData.username.trim(),
        password: formData.useGoogleLogin ? null : formData.password.trim(),
        useGoogleLogin: formData.useGoogleLogin,
        proxy: formData.proxy.type === 'No proxy' ? {
          type: 'No proxy',
          host: null,
          port: null,
          username: null,
          password: null,
          country: null,
          city: null
        } : {
          type: formData.proxy.type,
          host: formData.proxy.host.trim(),
          port: parseInt(formData.proxy.port),
          username: formData.proxy.username.trim() || null,
          password: formData.proxy.password.trim() || null,
          country: formData.proxy.country.trim() || null,
          city: formData.proxy.city.trim() || null
        },
        ipChecker: formData.ipChecker
      }

      await onSubmit(accountData)
      handleClose()
    } catch (error) {
      console.error('Error creating account:', error)
      alert('Có lỗi xảy ra khi tạo tài khoản')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    setFormData({
      username: '',
      password: '',
      useGoogleLogin: false,
      proxy: {
        type: 'HTTP',
        host: '',
        port: '',
        username: '',
        password: '',
        country: '',
        city: ''
      },
      ipChecker: 'IP2Location'
    })
    setProxyStatus(null)
    setProxyTestResult(null)
    setIsSubmitting(false)
    onClose()
  }

  const getProxyStatusText = () => {
    switch (proxyStatus) {
      case 'testing':
        return 'Testing proxy...'
      case 'active':
        return 'Connection test passed!'
      case 'inactive':
        return 'Connection failed'
      default:
        return ''
    }
  }

  if (!isOpen) return null

  return (
    <div className="modal-overlay" onClick={handleClose}>
      <div className="modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3 className="modal-title">Thêm tài khoản mới</h3>
          <button className="modal-close" onClick={handleClose}>
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="modal-body">
            {/* Google Login Option */}
            <div className="checkbox-group">
              <input
                type="checkbox"
                id="useGoogleLogin"
                checked={formData.useGoogleLogin}
                onChange={(e) => handleInputChange('useGoogleLogin', e.target.checked)}
              />
              <label htmlFor="useGoogleLogin">
                Đăng nhập bằng Google (bỏ qua username/password)
              </label>
            </div>

            {/* Account Information */}
            {!formData.useGoogleLogin && (
              <>
                <div className="form-row">
                  <div className="form-group">
                    <label className="form-label">Username *</label>
                    <input
                      type="text"
                      className="form-input"
                      value={formData.username}
                      onChange={(e) => handleInputChange('username', e.target.value)}
                      placeholder="Nhập username"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label className="form-label">Password *</label>
                    <input
                      type="password"
                      className="form-input"
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      placeholder="Nhập password"
                      required
                    />
                  </div>
                </div>
              </>
            )}

            {/* Proxy Information */}
            <h4 style={{ margin: '24px 0 16px 0', color: '#374151', fontSize: '16px' }}>
              Thông tin Proxy *
            </h4>

            {/* Proxy Type */}
            <div className="form-row">
              <div className="form-group">
                <label className="form-label">Proxy type</label>
                <select
                  className="form-input"
                  value={formData.proxy.type}
                  onChange={(e) => handleInputChange('proxy.type', e.target.value)}
                >
                  {proxyTypes.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="form-group">
                <label className="form-label">IP checker</label>
                <select
                  className="form-input"
                  value={formData.ipChecker}
                  onChange={(e) => handleInputChange('ipChecker', e.target.value)}
                >
                  {ipCheckers.map(checker => (
                    <option key={checker.value} value={checker.value}>
                      {checker.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Host:Port */}
            {formData.proxy.type !== 'No proxy' && (
              <div className="form-row">
                <div className="form-group" style={{ position: 'relative' }}>
                  <label className="form-label">Host:Port *</label>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <input
                      type="text"
                      className="form-input"
                      value={formData.proxy.host}
                      onChange={(e) => handleInputChange('proxy.host', e.target.value)}
                      placeholder="***************"
                      required
                      style={{ flex: 1 }}
                    />
                    <span style={{ color: '#6b7280', fontSize: '16px', fontWeight: 'bold' }}>:</span>
                    <input
                      type="number"
                      className="form-input"
                      value={formData.proxy.port}
                      onChange={(e) => handleInputChange('proxy.port', e.target.value)}
                      placeholder="13996"
                      required
                      style={{ flex: '0 0 100px' }}
                    />
                  </div>
                </div>
                <div className="form-group" style={{ display: 'flex', alignItems: 'flex-end' }}>
                  <button
                    type="button"
                    className="btn btn-secondary btn-sm"
                    onClick={handleTestProxy}
                    disabled={proxyStatus === 'testing' || !formData.proxy.host || !formData.proxy.port}
                    style={{ height: '40px', minWidth: '100px' }}
                  >
                    {proxyStatus === 'testing' ? 'Testing...' : 'Check Proxy'}
                  </button>
                </div>
              </div>
            )}

            {/* Proxy Authentication */}
            {formData.proxy.type !== 'No proxy' && (
              <div className="form-row">
                <div className="form-group">
                  <label className="form-label">Proxy username</label>
                  <input
                    type="text"
                    className="form-input"
                    value={formData.proxy.username}
                    onChange={(e) => handleInputChange('proxy.username', e.target.value)}
                    placeholder="user301105"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Proxy Password</label>
                  <input
                    type="password"
                    className="form-input"
                    value={formData.proxy.password}
                    onChange={(e) => handleInputChange('proxy.password', e.target.value)}
                    placeholder="e538k7"
                  />
                </div>
              </div>
            )}

            {/* Proxy Test Result */}
            {proxyStatus && (
              <div style={{
                marginTop: '16px',
                padding: '12px',
                backgroundColor: proxyStatus === 'active' ? '#d1fae5' : proxyStatus === 'testing' ? '#fef3c7' : '#fee2e2',
                borderRadius: '6px',
                border: `1px solid ${proxyStatus === 'active' ? '#10b981' : proxyStatus === 'testing' ? '#f59e0b' : '#ef4444'}`
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                  <span className={`proxy-status ${proxyStatus}`}>
                    {getProxyStatusText()}
                  </span>
                </div>

                {proxyTestResult && proxyStatus === 'active' && (
                  <div style={{ fontSize: '12px', color: '#065f46' }}>
                    <div><strong>IP:</strong> {proxyTestResult.ip || 'N/A'}</div>
                    <div><strong>Country/Region:</strong> {proxyTestResult.country || 'N/A'}</div>
                    <div><strong>Region:</strong> {proxyTestResult.region || '-'}</div>
                    <div><strong>City:</strong> {proxyTestResult.city || '-'}</div>
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="modal-footer">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Hủy
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Đang tạo...' : 'Tạo tài khoản'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default AddAccountModal
