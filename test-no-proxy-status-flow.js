#!/usr/bin/env node

/**
 * Test script to verify "No proxy" account status flow
 * <PERSON><PERSON><PERSON> tra luồng trạng thái của tài kho<PERSON>n "No proxy" từ tạo đến login
 */

const DatabaseManager = require('./backend/src/database/manager');
const WebSocketServer = require('./backend/src/server/websocket');
const AccountController = require('./backend/src/controllers/AccountController');
const TikTokLoginAutomation = require('./backend/src/automation/login');

class NoProxyStatusFlowTester {
  constructor() {
    this.db = new DatabaseManager();
    this.wsServer = new WebSocketServer(8083); // Different port for testing
    this.accountController = null;
    this.loginAutomation = null;
    this.testAccountId = null;
  }

  async initialize() {
    console.log('🚀 Initializing No Proxy Status Flow tester...');
    
    // Initialize database
    await this.db.initialize();
    console.log('✅ Database initialized');

    // Start WebSocket server
    this.wsServer.start();
    console.log('✅ WebSocket server started on port 8083');

    // Initialize controllers
    this.accountController = new AccountController(this.db, this.wsServer);
    this.loginAutomation = new TikTokLoginAutomation(this.wsServer, this.db);
    console.log('✅ Controllers initialized');
  }

  async testAccountCreation() {
    console.log('\n🧪 Step 1: Testing account creation...\n');

    const testAccountData = {
      username: null,
      password: null,
      useGoogleLogin: true,
      proxy: {
        type: 'No proxy',
        host: null,
        port: null,
        username: null,
        password: null,
        country: null,
        city: null
      },
      ipChecker: 'ip-api'
    };

    // Mock WebSocket connection
    const mockWs = {
      send: (data) => {
        const message = JSON.parse(data);
        console.log(`📡 WebSocket: ${message.type} - ${message.message}`);
      }
    };

    try {
      console.log('🔧 Creating account...');
      await this.accountController.createAccount(mockWs, testAccountData);

      // Get the created account - find the most recently created one
      const accounts = await this.db.getAccounts();
      const noProxyAccounts = accounts.filter(acc =>
        acc.useGoogleLogin === true &&
        acc.proxy.type === 'No proxy' &&
        acc.username === null
      );

      // Get the most recently created account (last in array)
      const createdAccount = noProxyAccounts[noProxyAccounts.length - 1];

      if (createdAccount) {
        this.testAccountId = createdAccount.id;
        console.log(`✅ Account created with ID: ${this.testAccountId}`);
        console.log(`📊 Initial status: ${createdAccount.status}`);
        
        if (createdAccount.status === 'not_logged_in') {
          console.log('✅ Correct initial status: not_logged_in');
        } else {
          console.log(`❌ Wrong initial status: ${createdAccount.status} (expected: not_logged_in)`);
        }
        
        return createdAccount;
      } else {
        throw new Error('Account was not found after creation');
      }

    } catch (error) {
      console.error('❌ Account creation failed:', error.message);
      throw error;
    }
  }

  async monitorStatusChanges(accountId, duration = 30000) {
    console.log(`\n🔍 Monitoring status changes for ${duration/1000} seconds...\n`);
    
    let lastStatus = null;
    const startTime = Date.now();
    
    while (Date.now() - startTime < duration) {
      try {
        const account = await this.db.getAccountById(accountId);
        if (account && account.status !== lastStatus) {
          const timestamp = new Date().toLocaleTimeString();
          console.log(`[${timestamp}] Status changed: ${lastStatus || 'initial'} → ${account.status}`);
          lastStatus = account.status;
          
          if (account.status === 'ready') {
            console.log('❌ PROBLEM: Account automatically changed to "ready" without login!');
            return false;
          }
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000)); // Check every second
      } catch (error) {
        console.error('Error monitoring status:', error.message);
      }
    }
    
    console.log('✅ No unexpected status changes detected');
    return true;
  }

  async testLoginProcess() {
    console.log('\n🧪 Step 2: Testing login process...\n');

    if (!this.testAccountId) {
      throw new Error('No test account ID available');
    }

    try {
      console.log('🔧 Starting login process...');
      
      // Mock WebSocket for login
      const mockWs = {
        send: (data) => {
          const message = JSON.parse(data);
          if (message.type === 'log') {
            console.log(`📝 Login [${message.level}]: ${message.message}`);
          }
        }
      };

      // Start login process (this should NOT automatically set status to ready)
      console.log('⚠️  Starting login automation - this should open browser but NOT auto-complete');
      
      // Start login in background
      const loginPromise = this.loginAutomation.loginAccount(this.testAccountId);
      
      // Monitor status for 10 seconds after login starts
      await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds for login to start
      
      const statusOk = await this.monitorStatusChanges(this.testAccountId, 10000);
      
      if (!statusOk) {
        console.log('❌ Status monitoring failed - account auto-completed');
        return false;
      }
      
      // Check final status
      const account = await this.db.getAccountById(this.testAccountId);
      console.log(`📊 Final status after login attempt: ${account.status}`);
      
      if (account.status === 'login_completed') {
        console.log('✅ Correct behavior: Status is "login_completed", waiting for manual completion');
        return true;
      } else if (account.status === 'ready') {
        console.log('❌ Wrong behavior: Status auto-changed to "ready"');
        return false;
      } else {
        console.log(`ℹ️  Status is "${account.status}" - this may be expected depending on login flow`);
        return true;
      }

    } catch (error) {
      console.error('❌ Login test failed:', error.message);
      
      // Check if account status changed despite error
      const account = await this.db.getAccountById(this.testAccountId);
      if (account.status === 'ready') {
        console.log('❌ CRITICAL: Account status changed to "ready" even after login error!');
        return false;
      }
      
      return true; // Error is expected for manual login
    }
  }

  async testCompleteLoginFlow() {
    console.log('\n🧪 Step 3: Testing complete login flow...\n');

    if (!this.testAccountId) {
      throw new Error('No test account ID available');
    }

    try {
      // First, manually set account to login_completed status
      await this.db.updateAccount(this.testAccountId, { status: 'login_completed' });
      console.log('🔧 Manually set account status to "login_completed"');

      // Mock WebSocket
      const mockWs = {
        send: (data) => {
          const message = JSON.parse(data);
          console.log(`📡 Complete Login: ${message.type} - ${message.message}`);
        }
      };

      // Test complete login
      console.log('🔧 Testing complete login...');
      await this.accountController.completeLogin(mockWs, { accountId: this.testAccountId }, this.loginAutomation);

      // Check final status
      const account = await this.db.getAccountById(this.testAccountId);
      console.log(`📊 Final status after complete login: ${account.status}`);

      if (account.status === 'ready') {
        console.log('✅ Correct: Status changed to "ready" only after manual completion');
        return true;
      } else {
        console.log(`❌ Wrong: Status is "${account.status}" instead of "ready"`);
        return false;
      }

    } catch (error) {
      console.error('❌ Complete login test failed:', error.message);
      return false;
    }
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    try {
      // Close any open browsers
      if (this.testAccountId) {
        await this.loginAutomation.closeBrowser(this.testAccountId);
      }
      
      // Delete test account
      if (this.testAccountId) {
        await this.db.deleteAccount(this.testAccountId);
        console.log('🗑️  Test account deleted');
      }
      
      // Stop WebSocket server
      this.wsServer.stop();
      
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.error('⚠️ Cleanup error:', error.message);
    }
  }

  async run() {
    try {
      await this.initialize();
      
      console.log('\n🧪 Starting No Proxy Status Flow Tests...\n');
      
      // Test 1: Account creation
      const account = await this.testAccountCreation();
      
      // Test 2: Login process (should not auto-complete)
      const loginOk = await this.testLoginProcess();
      
      // Test 3: Manual completion flow
      const completeOk = await this.testCompleteLoginFlow();
      
      // Results
      console.log('\n📊 Test Results:');
      console.log(`✅ Account Creation: PASS`);
      console.log(`${loginOk ? '✅' : '❌'} Login Process: ${loginOk ? 'PASS' : 'FAIL'}`);
      console.log(`${completeOk ? '✅' : '❌'} Complete Login: ${completeOk ? 'PASS' : 'FAIL'}`);
      
      if (loginOk && completeOk) {
        console.log('\n🎉 All tests PASSED! No proxy status flow is working correctly.');
      } else {
        console.log('\n❌ Some tests FAILED! There are issues with the status flow.');
      }
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
    } finally {
      await this.cleanup();
      process.exit(0);
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  const tester = new NoProxyStatusFlowTester();
  tester.run().catch(console.error);
}

module.exports = NoProxyStatusFlowTester;
