#!/usr/bin/env node

/**
 * Quick Test: No Automation Banner
 * <PERSON><PERSON>m tra nhanh xem có còn thông báo automation không
 */

const { chromium } = require('playwright');
const AntidetectManager = require('./src/antidetect/antidetect-manager');

async function testNoAutomationBanner() {
  console.log('🧪 Quick Test: Removing Automation Banner...\n');
  
  const antidetectManager = new AntidetectManager();
  let browser, page;
  
  try {
    // Load personas
    await antidetectManager.loadPersonas();
    const persona = antidetectManager.getRandomPersona();
    console.log(`🎭 Using persona: ${persona.platform}`);
    
    // Create TikTok-optimized browser
    const launchOptions = antidetectManager.createTikTokBrowserLaunchOptions();
    const contextOptions = await antidetectManager.createTikTokContextOptions(persona);
    
    console.log('🚀 Launching browser with enhanced antidetect...');
    browser = await chromium.launchPersistentContext('./test-profile-quick', {
      ...launchOptions,
      ...contextOptions
    });

    // Apply enhanced spoofing
    const spoofingScript = antidetectManager.createSpoofingScript(persona);
    await browser.addInitScript(spoofingScript);
    
    page = await browser.newPage();
    
    console.log('✅ Browser launched!');
    
    // Quick automation detection test
    const automationTest = await page.evaluate(() => {
      return {
        webdriver: navigator.webdriver,
        hasWebdriverProp: 'webdriver' in navigator,
        automationBanner: document.querySelector('[data-test-id="automation-infobar"]') !== null,
        infobars: document.querySelectorAll('.infobar, [class*="automation"]').length
      };
    });
    
    console.log('\n🔍 Automation Detection Results:');
    console.log(`   navigator.webdriver: ${automationTest.webdriver}`);
    console.log(`   'webdriver' in navigator: ${automationTest.hasWebdriverProp}`);
    console.log(`   Automation banner visible: ${automationTest.automationBanner}`);
    console.log(`   Infobar elements found: ${automationTest.infobars}`);
    
    // Navigate to TikTok
    console.log('\n🎵 Testing TikTok access...');
    await page.goto('https://www.tiktok.com', { 
      waitUntil: 'domcontentloaded',
      timeout: 15000 
    });
    
    console.log('✅ TikTok loaded successfully!');
    
    // Check for automation banner after navigation
    await page.waitForTimeout(2000);
    const postNavTest = await page.evaluate(() => {
      const banners = document.querySelectorAll('[data-test-id="automation-infobar"], .infobar, [class*="automation"]');
      return {
        bannerCount: banners.length,
        bannerTexts: Array.from(banners).map(b => b.textContent || b.innerText).filter(t => t)
      };
    });
    
    console.log('\n🔍 Post-Navigation Check:');
    console.log(`   Automation banners found: ${postNavTest.bannerCount}`);
    if (postNavTest.bannerTexts.length > 0) {
      console.log(`   Banner texts: ${postNavTest.bannerTexts.join(', ')}`);
    }
    
    if (postNavTest.bannerCount === 0 && !automationTest.webdriver) {
      console.log('\n🎉 SUCCESS: No automation detection found!');
      console.log('✅ Browser appears as normal Chrome browser');
      console.log('✅ TikTok should work normally');
    } else {
      console.log('\n⚠️  WARNING: Some automation indicators still present');
      console.log('❌ May need further optimization');
    }
    
    console.log('\n📝 Manual check: Look for "Chrome is being controlled by automated test software" banner');
    console.log('⌨️  Press Ctrl+C to close browser');
    
    // Keep browser open for manual inspection
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n👋 Closing browser...');
  process.exit(0);
});

// Run the test
testNoAutomationBanner().catch(error => {
  console.error('❌ Failed to run test:', error);
  process.exit(1);
});
