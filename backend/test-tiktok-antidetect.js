#!/usr/bin/env node

/**
 * Test TikTok Antidetect System (Enhanced 2025)
 * <PERSON><PERSON><PERSON> tra khả năng xem video TikTok và ẩn automation banner
 */

const { chromium } = require('playwright');
const AntidetectManager = require('./src/antidetect/antidetect-manager');

async function testTikTokAntidetect() {
  console.log('🧪 Testing Enhanced TikTok Antidetect System (2025)...\n');
  
  const antidetectManager = new AntidetectManager();
  let browser, page;
  
  try {
    // Load personas
    await antidetectManager.loadPersonas();
    const persona = antidetectManager.getRandomPersona();
    console.log(`🎭 Using persona: ${persona.platform} - ${persona.userAgent.slice(0, 60)}...`);
    console.log(`   Screen: ${persona.screen.width}x${persona.screen.height}`);
    console.log(`   WebGL: ${persona.webgl.vendor}`);
    console.log(`   Timezone: ${persona.timezone}\n`);
    
    // Create TikTok-optimized browser
    const launchOptions = antidetectManager.createTikTokBrowserLaunchOptions();
    const contextOptions = await antidetectManager.createTikTokContextOptions(persona);
    
    console.log('🚀 Launching TikTok-optimized browser...');
    browser = await chromium.launchPersistentContext('./test-profile', {
      ...launchOptions,
      ...contextOptions
    });

    // Apply enhanced spoofing
    const spoofingScript = antidetectManager.createSpoofingScript(persona);
    await browser.addInitScript(spoofingScript);
    
    page = await browser.newPage();
    
    console.log('✅ Browser launched successfully!');
    console.log('📋 Testing automation detection...\n');
    
    // Test 1: Check webdriver property
    const webdriverTest = await page.evaluate(() => {
      return {
        webdriver: navigator.webdriver,
        hasWebdriverProp: 'webdriver' in navigator,
        automationControlled: window.chrome && window.chrome.runtime && window.chrome.runtime.onConnect
      };
    });
    
    console.log('🔍 Webdriver Detection Test:');
    console.log(`   navigator.webdriver: ${webdriverTest.webdriver}`);
    console.log(`   'webdriver' in navigator: ${webdriverTest.hasWebdriverProp}`);
    console.log(`   Chrome automation controlled: ${webdriverTest.automationControlled}`);
    
    // Test 2: Check automation banner
    console.log('\n🔍 Automation Banner Test:');
    const bannerExists = await page.evaluate(() => {
      const infobars = document.querySelectorAll('[data-test-id="automation-infobar"], .infobar, [class*="automation"], [class*="infobar"]');
      return infobars.length > 0;
    });
    console.log(`   Automation banner visible: ${bannerExists}`);
    
    // Test 3: Navigate to TikTok
    console.log('\n🎵 Navigating to TikTok...');
    await page.goto('https://www.tiktok.com', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    console.log('✅ TikTok loaded successfully!');
    
    // Test 4: Check if videos can be played
    console.log('\n🎬 Testing video playability...');
    await page.waitForTimeout(3000); // Wait for page to load
    
    const videoTest = await page.evaluate(() => {
      const videos = document.querySelectorAll('video');
      const videoElements = Array.from(videos);
      
      return {
        videoCount: videoElements.length,
        hasVideoElements: videoElements.length > 0,
        videoSources: videoElements.map(v => ({
          src: v.src || 'no-src',
          readyState: v.readyState,
          paused: v.paused,
          muted: v.muted
        })).slice(0, 3) // First 3 videos only
      };
    });
    
    console.log(`   Video elements found: ${videoTest.videoCount}`);
    console.log(`   Videos playable: ${videoTest.hasVideoElements}`);
    if (videoTest.videoSources.length > 0) {
      console.log(`   Sample video states:`, videoTest.videoSources);
    }
    
    // Test 5: Check TikTok-specific detection
    console.log('\n🔍 TikTok Bot Detection Test:');
    const tiktokDetection = await page.evaluate(() => {
      return {
        hasByteAcrawler: typeof window.byted_acrawler !== 'undefined',
        hasTtq: typeof window.ttq !== 'undefined',
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        languages: navigator.languages
      };
    });
    
    console.log(`   ByteDance crawler detected: ${tiktokDetection.hasByteAcrawler}`);
    console.log(`   TikTok tracking (ttq): ${tiktokDetection.hasTtq}`);
    console.log(`   User Agent: ${tiktokDetection.userAgent.slice(0, 80)}...`);
    console.log(`   Platform: ${tiktokDetection.platform}`);
    console.log(`   Languages: ${tiktokDetection.languages.join(', ')}`);
    
    // Test 6: Canvas fingerprinting test
    console.log('\n🎨 Canvas Fingerprinting Test:');
    const canvasTest = await page.evaluate(() => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('TikTok Test 🎵', 2, 2);
      return canvas.toDataURL().slice(0, 50) + '...';
    });
    console.log(`   Canvas fingerprint: ${canvasTest}`);
    
    // Test 7: WebGL fingerprinting test
    console.log('\n🖥️  WebGL Fingerprinting Test:');
    const webglTest = await page.evaluate(() => {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (!gl) return 'WebGL not supported';
      
      return {
        vendor: gl.getParameter(gl.VENDOR),
        renderer: gl.getParameter(gl.RENDERER),
        version: gl.getParameter(gl.VERSION)
      };
    });
    console.log(`   WebGL Vendor: ${webglTest.vendor || 'N/A'}`);
    console.log(`   WebGL Renderer: ${webglTest.renderer || 'N/A'}`);
    console.log(`   WebGL Version: ${webglTest.version || 'N/A'}`);
    
    console.log('\n🎉 Test completed! Browser will remain open for manual inspection.');
    console.log('📝 Check the following:');
    console.log('   1. No "Chrome is being controlled by automated test software" banner');
    console.log('   2. TikTok videos should load and be playable');
    console.log('   3. Login functionality should work normally');
    console.log('   4. No bot detection warnings');
    console.log('\n⌨️  Press Ctrl+C to close the browser and exit.');
    
    // Keep browser open for manual testing
    await new Promise(() => {}); // Keep running indefinitely
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    // Browser will be closed by Ctrl+C
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n👋 Shutting down test...');
  process.exit(0);
});

// Run the test
testTikTokAntidetect().catch(error => {
  console.error('❌ Failed to run test:', error);
  process.exit(1);
});
