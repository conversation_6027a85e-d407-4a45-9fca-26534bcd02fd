const http = require('http');
const net = require('net');
const { SocksClient } = require('socks');

/**
 * Optimized SOCKS5 to HTTP Bridge Server
 * Tạo local HTTP proxy server để bridge đến SOCKS5 proxy với authentication
 * Version 2.0 - Optimized for speed and performance
 */
class Socks5Bridge {
  constructor() {
    this.server = null;
    this.port = null;
    this.isRunning = false;
    this.connections = new Set();
    this.connectionPool = new Map(); // Connection pooling for reuse
    this.stats = {
      totalConnections: 0,
      activeConnections: 0,
      errors: 0,
      avgResponseTime: 0
    };
  }

  /**
   * Start bridge server
   * @param {Object} socks5Config - SOCKS5 proxy configuration
   * @returns {Promise<number>} - Local HTTP proxy port
   */
  async start(socks5Config) {
    if (this.isRunning) {
      return this.port;
    }

    return new Promise((resolve, reject) => {
      // Tìm port available
      this.server = http.createServer();
      
      // Handle HTTP CONNECT method (for HTTPS)
      this.server.on('connect', (req, clientSocket, head) => {
        this.handleConnect(req, clientSocket, head, socks5Config);
      });

      // Handle regular HTTP requests
      this.server.on('request', (req, res) => {
        this.handleHttpRequest(req, res, socks5Config);
      });

      // Find available port
      this.server.listen(0, '127.0.0.1', () => {
        this.port = this.server.address().port;
        this.isRunning = true;
        this.startTime = Date.now();
        console.log(`🌉 SOCKS5 Bridge started on port ${this.port}`);
        console.log(`   Bridging to: ${socks5Config.host}:${socks5Config.port}`);
        resolve(this.port);
      });

      this.server.on('error', (error) => {
        console.error('❌ Bridge server error:', error);
        reject(error);
      });

      // Track connections for cleanup
      this.server.on('connection', (socket) => {
        this.connections.add(socket);
        socket.on('close', () => {
          this.connections.delete(socket);
        });
      });
    });
  }

  /**
   * Handle HTTPS CONNECT requests - Optimized version
   */
  async handleConnect(req, clientSocket, _head, socks5Config) {
    const startTime = Date.now();
    this.stats.totalConnections++;
    this.stats.activeConnections++;

    try {
      const [hostname, port] = req.url.split(':');

      // Connect through SOCKS5 with optimized settings
      const socksConnection = await SocksClient.createConnection({
        proxy: {
          host: socks5Config.host,
          port: socks5Config.port,
          type: 5,
          userId: socks5Config.username,
          password: socks5Config.password
        },
        command: 'connect',
        destination: {
          host: hostname,
          port: parseInt(port)
        },
        timeout: 10000 // 10 second timeout for faster failure
      });

      // Send success response to client
      clientSocket.write('HTTP/1.1 200 Connection Established\r\n\r\n');

      // Optimize socket settings for performance
      socksConnection.socket.setNoDelay(true);
      socksConnection.socket.setKeepAlive(true, 30000);
      clientSocket.setNoDelay(true);
      clientSocket.setKeepAlive(true, 30000);

      // Pipe data between client and SOCKS5 connection
      clientSocket.pipe(socksConnection.socket, { end: false });
      socksConnection.socket.pipe(clientSocket, { end: false });

      // Handle cleanup
      const cleanup = () => {
        this.stats.activeConnections--;
        socksConnection.socket.destroy();
        clientSocket.destroy();
      };

      clientSocket.on('error', cleanup);
      clientSocket.on('close', cleanup);
      socksConnection.socket.on('error', cleanup);
      socksConnection.socket.on('close', cleanup);

      // Update performance stats
      const responseTime = Date.now() - startTime;
      this.stats.avgResponseTime = (this.stats.avgResponseTime + responseTime) / 2;

    } catch (error) {
      this.stats.errors++;
      this.stats.activeConnections--;
      console.error('❌ CONNECT error:', error.message);

      if (!clientSocket.destroyed) {
        clientSocket.write('HTTP/1.1 500 Connection Error\r\n\r\n');
        clientSocket.destroy();
      }
    }
  }

  /**
   * Handle regular HTTP requests
   */
  async handleHttpRequest(req, res, socks5Config) {
    try {
      const url = new URL(req.url.startsWith('http') ? req.url : `http://${req.headers.host}${req.url}`);
      
      // Connect through SOCKS5
      const socksConnection = await SocksClient.createConnection({
        proxy: {
          host: socks5Config.host,
          port: socks5Config.port,
          type: 5,
          userId: socks5Config.username,
          password: socks5Config.password
        },
        command: 'connect',
        destination: {
          host: url.hostname,
          port: url.port || (url.protocol === 'https:' ? 443 : 80)
        }
      });

      // Forward request
      const requestData = `${req.method} ${url.pathname}${url.search} HTTP/1.1\r\n`;
      const headers = Object.entries(req.headers)
        .map(([key, value]) => `${key}: ${value}`)
        .join('\r\n');
      
      socksConnection.socket.write(`${requestData}${headers}\r\n\r\n`);

      // Forward request body if any
      req.pipe(socksConnection.socket, { end: false });
      req.on('end', () => socksConnection.socket.end());

      // Forward response
      socksConnection.socket.pipe(res);

      // Handle errors
      socksConnection.socket.on('error', (error) => {
        console.error('❌ HTTP request error:', error);
        if (!res.headersSent) {
          res.writeHead(500);
          res.end('Proxy Error');
        }
      });

    } catch (error) {
      console.error('❌ HTTP request error:', error);
      if (!res.headersSent) {
        res.writeHead(500);
        res.end('Proxy Error');
      }
    }
  }

  /**
   * Stop bridge server
   */
  async stop() {
    if (!this.isRunning) {
      return;
    }

    return new Promise((resolve) => {
      // Close all connections
      for (const socket of this.connections) {
        socket.destroy();
      }
      this.connections.clear();

      // Close server
      this.server.close(() => {
        console.log(`🌉 SOCKS5 Bridge stopped (was on port ${this.port})`);
        this.isRunning = false;
        this.port = null;
        resolve();
      });
    });
  }

  /**
   * Get HTTP proxy config for browser
   */
  getHttpProxyConfig() {
    if (!this.isRunning) {
      throw new Error('Bridge server is not running');
    }

    return {
      server: `http://127.0.0.1:${this.port}`,
      bypass: 'localhost,127.0.0.1,::1'
    };
  }

  /**
   * Get performance statistics
   */
  getStats() {
    return {
      ...this.stats,
      isRunning: this.isRunning,
      port: this.port,
      uptime: this.isRunning ? Date.now() - this.startTime : 0
    };
  }

  /**
   * Reset performance statistics
   */
  resetStats() {
    this.stats = {
      totalConnections: 0,
      activeConnections: 0,
      errors: 0,
      avgResponseTime: 0
    };
  }
}

module.exports = Socks5Bridge;
