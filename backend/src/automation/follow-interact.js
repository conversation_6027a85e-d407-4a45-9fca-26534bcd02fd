const { chromium } = require('playwright');
const path = require('path');
const { sleep, randomInt, getRandomComment, formatTime, isValidTikTokURL } = require('../utils');
const ProxyService = require('../services/ProxyService');

class FollowInteractAutomation {
  constructor(wsServer, dbManager) {
    this.wsServer = wsServer;
    this.dbManager = dbManager;
    this.activeSessions = new Map(); // accountId -> session info
    this.isRunning = false;
    this.rateLimiter = new Map(); // accountId -> rate limit info
    this.proxyService = new ProxyService();
  }

  /**
   * Bắt đầu automation cho nhiều tài khoản với window positioning
   */
  async startAutomation(accountIds, targetProfile) {
    try {
      if (!isValidTikTokURL(targetProfile)) {
        throw new Error('Invalid TikTok profile URL');
      }

      this.isRunning = true;
      this.wsServer.sendLog('info', `Starting automation for ${accountIds.length} accounts with visible browsers`);

      // Khởi tạo rate limiter cho các tài kho<PERSON>n
      for (const accountId of accountIds) {
        this.initializeRateLimiter(accountId);
      }

      // Tính toán window positioning cho multiple browsers
      this.calculateWindowPositions(accountIds.length);

      // Chạy automation song song với giới hạn concurrency (giảm để tối ưu RAM)
      const maxConcurrent = Math.min(2, accountIds.length); // Giảm xuống 2 để tối ưu RAM
      const chunks = this.chunkArray(accountIds, maxConcurrent);

      this.wsServer.sendLog('info', `Running ${maxConcurrent} browsers concurrently to optimize performance`);

      for (const chunk of chunks) {
        if (!this.isRunning) break;

        const promises = chunk.map((accountId, index) =>
          this.runAccountAutomation(accountId, targetProfile, index)
            .catch(error => {
              this.wsServer.sendLog('error', `Automation failed for ${accountId}: ${error.message}`, accountId);
            })
        );

        await Promise.all(promises);

        // Nghỉ giữa các batch với memory cleanup
        if (chunks.indexOf(chunk) < chunks.length - 1) {
          this.wsServer.sendLog('info', 'Cleaning up memory between batches...');
          if (global.gc) {
            global.gc();
          }
          await sleep(10000, 15000); // Tăng thời gian nghỉ để cleanup memory
        }
      }

      this.wsServer.sendLog('success', 'Automation completed for all accounts');
    } catch (error) {
      this.wsServer.sendLog('error', `Automation error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tính toán vị trí cửa sổ cho multiple browsers
   */
  calculateWindowPositions(accountCount) {
    const windowWidth = 395; // Mobile width + padding
    const windowHeight = 767; // Mobile height + padding
    const screenWidth = 1920; // Assume common screen width
    const screenHeight = 1080; // Assume common screen height

    const maxColumns = Math.floor(screenWidth / windowWidth);
    const maxRows = Math.floor(screenHeight / windowHeight);

    this.windowPositions = [];

    for (let i = 0; i < accountCount; i++) {
      const col = i % maxColumns;
      const row = Math.floor(i / maxColumns) % maxRows;

      this.windowPositions.push({
        x: col * windowWidth,
        y: row * windowHeight
      });
    }

    this.wsServer.sendLog('info', `Calculated window positions for ${accountCount} browsers (${maxColumns}x${maxRows} grid)`);
  }

  /**
   * Chạy automation cho một tài khoản với memory optimization
   */
  async runAccountAutomation(accountId, targetProfile, windowIndex = 0) {
    let browser = null;
    try {
      const account = await this.dbManager.getAccountById(accountId);
      if (!account) {
        throw new Error(`Account ${accountId} not found`);
      }

      // Debug logging for account data
      console.log(`🔍 Account data for ${accountId}:`, {
        username: account.username,
        status: account.status,
        hasProxy: !!account.proxy,
        proxyType: account.proxy?.type,
        proxyHost: account.proxy?.host,
        proxyPort: account.proxy?.port,
        hasProxyAuth: !!(account.proxy?.username && account.proxy?.password)
      });

      if (account.status !== 'ready') {
        throw new Error(`Account ${account.username} is not ready (status: ${account.status})`);
      }

      // Kiểm tra rate limit
      if (!(await this.checkRateLimit(accountId))) {
        this.wsServer.sendLog('warning', `Rate limit reached for ${account.username}`, accountId);
        await this.dbManager.updateAccount(accountId, { status: 'limit_reached' });
        this.wsServer.sendAccountStatusUpdate(accountId, 'limit_reached');
        return;
      }

      this.wsServer.sendLog('info', `Starting automation for ${account.username}`, accountId);
      await this.dbManager.updateAccount(accountId, { status: 'running_interact' });
      this.wsServer.sendAccountStatusUpdate(accountId, 'running_interact');

      // Khởi tạo browser session với memory tracking và window positioning
      browser = await this.initializeBrowser(account, windowIndex);
      this.activeSessions.set(accountId, {
        browser,
        startTime: Date.now(),
        memoryUsage: process.memoryUsage()
      });

      // Periodic memory cleanup
      const memoryCleanupInterval = setInterval(async () => {
        try {
          if (global.gc) {
            global.gc();
          }
          // Log memory usage
          const memUsage = process.memoryUsage();
          console.log(`Memory usage for ${accountId}: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
        } catch (error) {
          // Ignore cleanup errors
        }
      }, 30000); // Every 30 seconds

      try {
        // Send stage update: scraping followers
        this.sendAutomationStage(accountId, 'scraping_followers', { targetProfile });

        // Lấy danh sách followers của target
        const followers = await this.scrapeFollowers(browser, targetProfile, accountId);

        if (followers.length === 0) {
          throw new Error('No followers found or unable to access followers list');
        }

        this.wsServer.sendLog('info', `Found ${followers.length} followers to interact with`, accountId);

        // Send stage update: starting interactions
        this.sendAutomationStage(accountId, 'starting_interactions', {
          followersFound: followers.length,
          maxFollows: Math.min(settings.maxFollowsPerSession || 10, followers.length)
        });

        // Tương tác với từng follower
        const settings = await this.dbManager.getSettings();
        const maxFollows = Math.min(settings.maxFollowsPerSession || 10, followers.length);

        for (let i = 0; i < maxFollows && this.isRunning; i++) {
          const follower = followers[i];

          try {
            // Send detailed progress update
            this.sendAutomationProgress(accountId, {
              currentFollower: i + 1,
              totalFollowers: maxFollows,
              followerUsername: follower.username,
              stage: 'starting_interaction'
            });

            this.wsServer.sendLog('info', `Processing follower ${i + 1}/${maxFollows}: ${follower.username}`, accountId);

            await this.interactWithUser(browser, follower, accountId, settings);

            // Send success update
            this.sendAutomationProgress(accountId, {
              currentFollower: i + 1,
              totalFollowers: maxFollows,
              followerUsername: follower.username,
              stage: 'interaction_completed'
            });

            // Cập nhật stats
            await this.updateAccountStats(accountId);

            // Memory cleanup after each interaction
            if (global.gc && i % 3 === 0) {
              global.gc();
            }

            // Delay giữa các tương tác với progress update
            const delay = randomInt(45000, 75000); // 45-75 giây (tăng để giảm tải)

            this.sendAutomationProgress(accountId, {
              currentFollower: i + 1,
              totalFollowers: maxFollows,
              stage: 'waiting',
              waitTime: Math.round(delay/1000)
            });

            this.wsServer.sendLog('info', `Waiting ${Math.round(delay/1000)}s before next interaction (${i + 1}/${maxFollows} completed)`, accountId);
            await sleep(delay);

          } catch (error) {
            // Send error update
            this.sendAutomationProgress(accountId, {
              currentFollower: i + 1,
              totalFollowers: maxFollows,
              followerUsername: follower.username,
              stage: 'interaction_failed',
              error: error.message
            });

            this.wsServer.sendLog('error', `Failed to interact with ${follower.username}: ${error.message}`, accountId);
            // Continue with next follower
            continue;
          }
        }

        // Hoàn thành
        this.sendAutomationStage(accountId, 'automation_completed', {
          totalInteractions: maxFollows,
          duration: Date.now() - this.activeSessions.get(accountId)?.startTime
        });

        await this.dbManager.updateAccount(accountId, { status: 'ready' });
        this.wsServer.sendAccountStatusUpdate(accountId, 'ready');
        this.wsServer.sendLog('success', `Automation completed for ${account.username} - ${maxFollows} interactions finished`, accountId);

      } finally {
        // Clear memory cleanup interval
        clearInterval(memoryCleanupInterval);

        // Cleanup browser with proper error handling
        await this.closeBrowserSession(accountId);
      }

    } catch (error) {
      await this.dbManager.updateAccount(accountId, { status: 'error' });
      this.wsServer.sendAccountStatusUpdate(accountId, 'error');

      // Ensure browser cleanup on error
      if (browser) {
        try {
          await this.closeBrowserSession(accountId);
        } catch (cleanupError) {
          console.error(`Error during browser cleanup for ${accountId}:`, cleanupError);
        }
      }

      throw error;
    }
  }

  /**
   * Khởi tạo browser với mobile view và window positioning
   */
  async initializeBrowser(account, windowIndex = 0) {
    const profileDir = path.join(__dirname, '../../profiles', account.id);

    // Create proxy configuration using ProxyService
    let proxyConfig = undefined;
    if (account.proxy && account.proxy.type !== 'No proxy') {
      try {
        // Debug logging
        console.log(`🔍 Proxy debug for account ${account.id}:`, {
          type: account.proxy.type,
          hasUsername: !!account.proxy.username,
          hasPassword: !!account.proxy.password,
          host: account.proxy.host,
          port: account.proxy.port
        });

        // Check if this is SOCKS5 with authentication
        if (account.proxy.type.toLowerCase() === 'socks5' && account.proxy.username && account.proxy.password) {
          // Use bridge method for SOCKS5 with auth
          console.log(`🌉 Using bridge method for SOCKS5 with auth for account ${account.id}`);
          proxyConfig = await this.proxyService.createProxyConfigWithBridge(account.proxy, account.id);
          console.log(`✅ SOCKS5 bridge configured for account ${account.id}: ${proxyConfig.server}`);
        } else {
          // Use regular method for other proxy types
          console.log(`🔧 Using regular method for proxy type ${account.proxy.type} for account ${account.id}`);
          proxyConfig = this.proxyService.createProxyConfig(account.proxy);
        }
      } catch (error) {
        console.error(`❌ Proxy configuration failed for account ${account.id}: ${error.message}`);
        throw error;
      }
    }

    // Mobile viewport configuration for better multi-window viewing
    const mobileViewport = {
      width: 375,   // iPhone width
      height: 667   // iPhone height
    };

    // Desktop user agent to avoid "Open App Store?" popup while keeping mobile viewport
    const desktopUserAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';

    // Get window position for this browser
    const windowPosition = this.windowPositions && this.windowPositions[windowIndex]
      ? this.windowPositions[windowIndex]
      : { x: windowIndex * 400, y: 0 };

    // Sử dụng enhanced antidetect cho follow automation
    const AntidetectManager = require('../antidetect/antidetect-manager');
    const antidetectManager = new AntidetectManager();

    // Load personas và chọn persona phù hợp
    await antidetectManager.loadPersonas();
    const persona = antidetectManager.getRandomPersona();

    // Tạo TikTok-optimized options
    const launchOptions = antidetectManager.createTikTokBrowserLaunchOptions();
    const contextOptions = await antidetectManager.createTikTokContextOptions(persona, proxy, accountId);

    const browser = await chromium.launchPersistentContext(profileDir, {
      ...launchOptions,
      ...contextOptions,
      headless: false, // Hiển thị browser để user có thể xem quá trình
      viewport: mobileViewport,
      userAgent: desktopUserAgent, // Use desktop UA to avoid app store popup
      proxy: proxyConfig,
      // Override với args tối ưu cho performance và window positioning
      args: [
        ...launchOptions.args,
        // Window positioning for multiple browsers
        `--window-size=${mobileViewport.width + 20},${mobileViewport.height + 100}`,
        `--window-position=${windowPosition.x},${windowPosition.y}`,
        // Memory optimization for multiple browsers
        '--max_old_space_size=512', // Giới hạn memory per process
        '--memory-pressure-off' // Tắt memory pressure
      ]
    });

    // Apply enhanced spoofing script
    const spoofingScript = antidetectManager.createSpoofingScript(persona);
    await browser.addInitScript(spoofingScript);

    // Tối ưu hóa resource loading - block nhiều resource hơn để tiết kiệm RAM
    const page = await browser.newPage();
    await page.route('**/*', (route) => {
      const resourceType = route.request().resourceType();
      const url = route.request().url();

      // Block nhiều loại resource để tiết kiệm bandwidth và memory
      if (['image', 'font', 'media'].includes(resourceType)) {
        // Block tất cả images, fonts, media để tiết kiệm RAM
        route.abort();
      } else if (resourceType === 'stylesheet' && !url.includes('tiktok')) {
        // Block external stylesheets
        route.abort();
      } else if (url.includes('analytics') || url.includes('ads') || url.includes('tracking')) {
        // Block analytics và ads
        route.abort();
      } else {
        route.continue();
      }
    });

    // Set mobile device emulation
    await page.emulateMedia({ media: 'screen' });

    this.wsServer.sendLog('info', `Browser initialized at position (${windowPosition.x}, ${windowPosition.y}) with mobile view`, account.id);

    // Send browser window info
    this.sendBrowserWindowInfo(account.id, {
      position: windowPosition,
      viewport: mobileViewport,
      userAgent: desktopUserAgent,
      windowIndex
    });

    return browser;
  }

  /**
   * Handle TikTok popups (App Store, Login, etc.)
   */
  async handleTikTokPopups(page, accountId) {
    try {
      // Handle "Open App Store?" popup
      const appStorePopup = page.locator('text="Open App Store?"').first();
      if (await appStorePopup.isVisible({ timeout: 2000 })) {
        this.wsServer.sendLog('info', 'Dismissing "Open App Store?" popup', accountId);
        const cancelButton = page.locator('button:has-text("Cancel")').first();
        if (await cancelButton.isVisible()) {
          await cancelButton.click();
          await sleep(1000, 2000);
        }
      }

      // Handle login popup
      const loginPopup = page.locator('[data-e2e="login-modal"]').first();
      if (await loginPopup.isVisible({ timeout: 2000 })) {
        this.wsServer.sendLog('info', 'Dismissing login popup', accountId);
        const closeButton = page.locator('[data-e2e="modal-close-inner-button"]').first();
        if (await closeButton.isVisible()) {
          await closeButton.click();
          await sleep(1000, 2000);
        }
      }

      // Handle cookie consent
      const cookieConsent = page.locator('text="Accept all"').first();
      if (await cookieConsent.isVisible({ timeout: 2000 })) {
        this.wsServer.sendLog('info', 'Accepting cookies', accountId);
        await cookieConsent.click();
        await sleep(1000, 2000);
      }

      // Handle age verification
      const ageVerification = page.locator('text="I am 18 or older"').first();
      if (await ageVerification.isVisible({ timeout: 2000 })) {
        this.wsServer.sendLog('info', 'Confirming age verification', accountId);
        await ageVerification.click();
        await sleep(1000, 2000);
      }

      // Handle any generic modal close buttons
      const genericCloseButtons = [
        '[aria-label="Close"]',
        '[data-e2e="close-button"]',
        'button[aria-label="Close"]',
        '.close-button'
      ];

      for (const selector of genericCloseButtons) {
        const closeBtn = page.locator(selector).first();
        if (await closeBtn.isVisible({ timeout: 1000 })) {
          this.wsServer.sendLog('info', `Closing modal with selector: ${selector}`, accountId);
          await closeBtn.click();
          await sleep(500, 1000);
          break;
        }
      }

    } catch (error) {
      // Ignore popup handling errors
      this.wsServer.sendLog('warning', `Popup handling warning: ${error.message}`, accountId);
    }
  }

  /**
   * Scrape danh sách followers với pagination
   */
  async scrapeFollowers(browser, targetProfile, accountId, maxFollowers = 100) {
    const page = await browser.newPage();
    const followers = [];

    try {
      this.wsServer.sendLog('info', `Navigating to ${targetProfile}`, accountId);
      await page.goto(targetProfile, { waitUntil: 'domcontentloaded', timeout: 30000 });
      await sleep(2000, 4000);

      // Handle any popups that might appear
      await this.handleTikTokPopups(page, accountId);

      // Wait a bit more for page to stabilize
      await sleep(2000, 3000);

      // Tìm và click vào followers count
      const followersButton = page.locator('[data-e2e="followers-count"]').first();
      if (!(await followersButton.isVisible())) {
        // Try alternative selectors for followers count
        const altSelectors = [
          '[data-e2e="followers-count"]',
          'strong[data-e2e="followers-count"]',
          '[title*="Followers"]',
          'a[href*="/followers"]'
        ];

        let found = false;
        for (const selector of altSelectors) {
          const element = page.locator(selector).first();
          if (await element.isVisible({ timeout: 3000 })) {
            await element.click();
            found = true;
            break;
          }
        }

        if (!found) {
          throw new Error('Cannot find followers button - profile may be private or page not loaded properly');
        }
      } else {
        await followersButton.click();
      }

      await sleep(3000, 5000);

      // Handle popups again after clicking followers
      await this.handleTikTokPopups(page, accountId);

      this.wsServer.sendLog('info', 'Scraping followers list...', accountId);

      // Scroll và collect followers
      let lastCount = 0;
      let stableCount = 0;

      while (followers.length < maxFollowers && stableCount < 3) {
        // Lấy followers hiện tại trên trang
        const followerElements = await page.locator('[data-e2e="user-item"]').all();

        for (const element of followerElements) {
          if (followers.length >= maxFollowers) break;

          try {
            const usernameElement = element.locator('[data-e2e="user-link"]');
            const username = await usernameElement.getAttribute('href');

            if (username && !followers.find(f => f.username === username)) {
              followers.push({
                username: username.replace('/@', ''),
                profileUrl: `https://www.tiktok.com${username}`
              });
            }
          } catch (error) {
            // Skip invalid elements
            continue;
          }
        }

        // Scroll để load thêm
        await page.evaluate(() => {
          window.scrollTo(0, document.body.scrollHeight);
        });
        await sleep(2000, 3000);

        // Kiểm tra xem có load thêm được không
        if (followers.length === lastCount) {
          stableCount++;
        } else {
          stableCount = 0;
          lastCount = followers.length;
        }

        this.wsServer.sendLog('info', `Collected ${followers.length} followers...`, accountId);
      }

      return followers.slice(0, maxFollowers);

    } catch (error) {
      this.wsServer.sendLog('error', `Failed to scrape followers: ${error.message}`, accountId);
      return [];
    } finally {
      await page.close();
    }
  }

  /**
   * Tương tác với một user với human-like behavior
   */
  async interactWithUser(browser, follower, accountId, settings) {
    const page = await browser.newPage();

    try {
      this.wsServer.sendLog('info', `Interacting with ${follower.username}`, accountId);

      // Truy cập profile của user với human-like navigation
      await page.goto(follower.profileUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });

      // Handle any popups immediately after navigation
      await this.handleTikTokPopups(page, accountId);

      // Human-like profile exploration
      await sleep(randomInt(2000, 5000));
      await this.exploreProfile(page);

      // Kiểm tra xem đã follow chưa
      const isAlreadyFollowing = await this.checkIfFollowing(page);
      if (isAlreadyFollowing) {
        this.wsServer.sendLog('info', `Already following ${follower.username}, skipping`, accountId);
        // Still do some human-like browsing before leaving
        await this.humanScroll(page);
        await sleep(randomInt(1000, 3000));
        return;
      }

      // Lấy danh sách videos
      const videos = await this.getProfileVideos(page, settings.videosToWatch || 3);

      if (videos.length === 0) {
        this.wsServer.sendLog('warning', `No videos found for ${follower.username}`, accountId);
        // Still try to follow even without videos
        await this.followUser(page, follower.username, accountId);
        return;
      }

      // Xem và tương tác với videos với realistic timing
      const videosToWatch = Math.min(videos.length, settings.videosToWatch || 3);
      for (let i = 0; i < videosToWatch; i++) {
        this.wsServer.sendLog('info', `Watching video ${i + 1}/${videosToWatch}`, accountId);

        await this.watchAndInteractWithVideo(page, videos[i], accountId, settings);

        // Human-like delay between videos
        if (i < videosToWatch - 1) {
          const delay = randomInt(8000, 15000); // 8-15 seconds between videos
          this.wsServer.sendLog('info', `Waiting ${Math.round(delay/1000)}s before next video`, accountId);
          await sleep(delay);

          // Return to profile between videos
          await page.goto(follower.profileUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
          await this.handleTikTokPopups(page, accountId);
          await sleep(randomInt(1000, 3000));
        }
      }

      // Return to profile for following
      await page.goto(follower.profileUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
      await this.handleTikTokPopups(page, accountId);
      await sleep(randomInt(2000, 4000));

      // Follow user with human-like behavior
      await this.followUser(page, follower.username, accountId);

    } catch (error) {
      this.wsServer.sendLog('error', `Error interacting with ${follower.username}: ${error.message}`, accountId);
      throw error;
    } finally {
      await page.close();
    }
  }

  /**
   * Explore profile like a human would
   */
  async exploreProfile(page) {
    try {
      // Scroll to see profile info
      await this.humanScroll(page);
      await sleep(randomInt(1000, 2000));

      // Look at follower/following counts (human curiosity)
      if (Math.random() < 0.3) {
        const followersCount = page.locator('[data-e2e="followers-count"]').first();
        if (await followersCount.isVisible()) {
          const box = await followersCount.boundingBox();
          if (box) {
            await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2);
            await sleep(randomInt(500, 1500));
          }
        }
      }

      // Scroll to see videos
      await this.humanScroll(page);
      await sleep(randomInt(1000, 2000));

    } catch (error) {
      // Ignore exploration errors
    }
  }

  /**
   * Kiểm tra xem đã follow user chưa
   */
  async checkIfFollowing(page) {
    try {
      const followButton = page.locator('[data-e2e="follow-button"]').first();
      const isVisible = await followButton.isVisible();
      return !isVisible; // Nếu không thấy nút follow thì đã follow rồi
    } catch (error) {
      return false;
    }
  }

  /**
   * Lấy danh sách videos từ profile
   */
  async getProfileVideos(page, maxVideos = 3) {
    try {
      const videos = [];
      const videoElements = await page.locator('[data-e2e="user-post-item"]').all();

      for (let i = 0; i < Math.min(videoElements.length, maxVideos); i++) {
        const videoElement = videoElements[i];
        const videoLink = await videoElement.locator('a').first().getAttribute('href');

        if (videoLink) {
          videos.push({
            url: `https://www.tiktok.com${videoLink}`,
            element: videoElement
          });
        }
      }

      return videos;
    } catch (error) {
      return [];
    }
  }

  /**
   * Xem và tương tác với video với human-like behavior
   */
  async watchAndInteractWithVideo(page, video, accountId, settings) {
    try {
      this.wsServer.sendLog('info', `Watching video: ${video.url}`, accountId);

      // Mở video với human-like navigation
      await page.goto(video.url, { waitUntil: 'domcontentloaded', timeout: 30000 });

      // Handle popups immediately after video page load
      await this.handleTikTokPopups(page, accountId);

      // Wait for video to load and simulate user waiting
      await sleep(randomInt(2000, 5000));

      // Human-like initial interaction - scroll to see video properly
      await this.humanScroll(page);
      await sleep(randomInt(1000, 2000));

      // Xem video trong thời gian ngẫu nhiên với variation
      const baseWatchTime = (settings.watchTimeSeconds || 30) * 1000;
      const watchTime = randomInt(
        Math.max(10000, baseWatchTime * 0.6),
        baseWatchTime * 1.4
      );

      this.wsServer.sendLog('info', `Watching for ${Math.round(watchTime/1000)}s with human-like behavior`, accountId);

      // Simulate watching behavior with more realistic interactions
      await this.simulateWatching(page, watchTime);

      // More realistic interaction chances
      const likeChance = randomInt(20, 40) / 100; // 20-40% chance
      const commentChance = randomInt(10, 25) / 100; // 10-25% chance

      // Random chance to like video
      if (Math.random() < likeChance) {
        await sleep(randomInt(1000, 3000)); // Hesitation before liking
        await this.likeVideo(page, accountId);
      }

      // Random chance to comment (after some delay)
      if (Math.random() < commentChance) {
        await sleep(randomInt(2000, 5000)); // More hesitation before commenting
        await this.commentOnVideo(page, accountId);
      }

      // Final human-like action before leaving
      if (Math.random() < 0.3) {
        await this.humanScroll(page);
      }

    } catch (error) {
      this.wsServer.sendLog('error', `Error watching video: ${error.message}`, accountId);
    }
  }

  /**
   * Simulate natural watching behavior with human-like interactions
   */
  async simulateWatching(page, duration) {
    const startTime = Date.now();
    const segments = Math.floor(duration / 3000); // Chia thành các đoạn 3 giây

    for (let i = 0; i < segments && Date.now() - startTime < duration; i++) {
      // Random human-like actions
      const action = Math.random();

      if (action < 0.2) {
        // Random scroll (20% chance)
        await this.humanScroll(page);
      } else if (action < 0.3) {
        // Random mouse movement (10% chance)
        await this.humanMouseMove(page);
      } else if (action < 0.35) {
        // Pause video briefly (5% chance)
        await this.pauseAndResumeVideo(page);
      } else if (action < 0.4) {
        // Random tap on screen (5% chance)
        await this.randomTap(page);
      }

      // Variable delay between actions
      const delay = randomInt(2000, 4000);
      await sleep(delay);
    }
  }

  /**
   * Human-like scrolling behavior
   */
  async humanScroll(page) {
    try {
      const scrollAmount = randomInt(-100, 150);
      const scrollSteps = randomInt(2, 4);

      for (let i = 0; i < scrollSteps; i++) {
        await page.evaluate((amount) => {
          window.scrollBy(0, amount / scrollSteps);
        }, scrollAmount);
        await sleep(100, 300);
      }
    } catch (error) {
      // Ignore scroll errors
    }
  }

  /**
   * Human-like mouse movement
   */
  async humanMouseMove(page) {
    try {
      const viewport = page.viewportSize();
      const x = randomInt(50, viewport.width - 50);
      const y = randomInt(50, viewport.height - 50);

      await page.mouse.move(x, y, { steps: randomInt(5, 15) });
      await sleep(100, 500);
    } catch (error) {
      // Ignore mouse errors
    }
  }

  /**
   * Pause and resume video to simulate user interaction
   */
  async pauseAndResumeVideo(page) {
    try {
      // Try to find and click video to pause
      const video = page.locator('video').first();
      if (await video.isVisible()) {
        await video.click();
        await sleep(randomInt(1000, 3000)); // Pause for 1-3 seconds
        await video.click(); // Resume
      }
    } catch (error) {
      // Ignore video interaction errors
    }
  }

  /**
   * Random tap on screen to simulate mobile interaction
   */
  async randomTap(page) {
    try {
      const viewport = page.viewportSize();
      const x = randomInt(100, viewport.width - 100);
      const y = randomInt(200, viewport.height - 200);

      await page.mouse.click(x, y);
      await sleep(200, 500);
    } catch (error) {
      // Ignore tap errors
    }
  }

  /**
   * Like video
   */
  async likeVideo(page, accountId) {
    try {
      const likeButton = page.locator('[data-e2e="like-button"]').first();
      if (await likeButton.isVisible()) {
        await likeButton.click();
        await sleep(1000, 2000);
        this.wsServer.sendLog('info', 'Liked video', accountId);
      }
    } catch (error) {
      // Ignore like errors
    }
  }

  /**
   * Comment on video
   */
  async commentOnVideo(page, accountId) {
    try {
      const comments = await this.dbManager.getComments();
      if (comments.length === 0) return;

      const commentText = getRandomComment(comments);

      // Tìm comment input
      const commentInput = page.locator('[data-e2e="comment-input"]').first();
      if (await commentInput.isVisible()) {
        await commentInput.fill(commentText);
        await sleep(1000, 2000);

        // Submit comment
        const submitButton = page.locator('[data-e2e="comment-post"]').first();
        if (await submitButton.isVisible()) {
          await submitButton.click();
          await sleep(2000, 3000);
          this.wsServer.sendLog('info', `Commented: "${commentText}"`, accountId);
        }
      }
    } catch (error) {
      // Ignore comment errors
      this.wsServer.sendLog('warning', `Failed to comment: ${error.message}`, accountId);
    }
  }

  /**
   * Follow user with human-like behavior
   */
  async followUser(page, username, accountId) {
    try {
      this.wsServer.sendLog('info', `Following ${username}`, accountId);
      await this.dbManager.updateAccount(accountId, { status: 'running_follow' });
      this.wsServer.sendAccountStatusUpdate(accountId, 'running_follow');

      // Handle any popups before attempting to follow
      await this.handleTikTokPopups(page, accountId);

      // Human-like hesitation before following
      await this.humanHesitation(page);

      const followButton = page.locator('[data-e2e="follow-button"]').first();
      if (await followButton.isVisible()) {
        // Move mouse to button area first
        const buttonBox = await followButton.boundingBox();
        if (buttonBox) {
          await page.mouse.move(
            buttonBox.x + buttonBox.width / 2 + randomInt(-5, 5),
            buttonBox.y + buttonBox.height / 2 + randomInt(-5, 5),
            { steps: randomInt(3, 8) }
          );
          await sleep(randomInt(500, 1500));
        }

        // Click with human-like timing
        await followButton.click();
        await sleep(randomInt(2000, 4000));

        // Verify follow success
        const isFollowing = await this.checkIfFollowing(page);
        if (isFollowing) {
          this.wsServer.sendLog('success', `Successfully followed ${username}`, accountId);

          // Small celebration scroll or movement
          await this.humanScroll(page);
          return true;
        } else {
          throw new Error('Follow action may have failed');
        }
      } else {
        throw new Error('Follow button not found');
      }
    } catch (error) {
      this.wsServer.sendLog('error', `Failed to follow ${username}: ${error.message}`, accountId);
      return false;
    }
  }

  /**
   * Human-like hesitation before important actions
   */
  async humanHesitation(page) {
    // Random small movements and pauses
    const actions = randomInt(1, 3);
    for (let i = 0; i < actions; i++) {
      const action = Math.random();
      if (action < 0.4) {
        await this.humanMouseMove(page);
      } else if (action < 0.7) {
        await this.humanScroll(page);
      }
      await sleep(randomInt(300, 1000));
    }
  }

  /**
   * Rate limiting functions
   */
  initializeRateLimiter(accountId) {
    const now = Date.now();
    this.rateLimiter.set(accountId, {
      dailyCount: 0,
      sessionCount: 0,
      lastReset: now,
      lastAction: now
    });
  }

  async checkRateLimit(accountId) {
    const settings = await this.dbManager.getSettings();
    const limits = this.rateLimiter.get(accountId);

    if (!limits) return false;

    const now = Date.now();
    const oneDayMs = 24 * 60 * 60 * 1000;

    // Reset daily count if needed
    if (now - limits.lastReset > oneDayMs) {
      limits.dailyCount = 0;
      limits.lastReset = now;
    }

    // Check limits
    if (limits.dailyCount >= (settings.maxFollowsPerDay || 50)) {
      return false;
    }

    if (limits.sessionCount >= (settings.maxFollowsPerSession || 10)) {
      return false;
    }

    return true;
  }

  async updateAccountStats(accountId) {
    const limits = this.rateLimiter.get(accountId);
    if (limits) {
      limits.dailyCount++;
      limits.sessionCount++;
      limits.lastAction = Date.now();
    }

    // Update database
    const account = await this.dbManager.getAccountById(accountId);
    if (account) {
      await this.dbManager.updateAccount(accountId, {
        stats: {
          ...account.stats,
          followsToday: limits.dailyCount,
          followsThisSession: limits.sessionCount,
          lastActivity: new Date().toISOString()
        }
      });
    }
  }

  /**
   * Utility functions
   */
  chunkArray(array, chunkSize) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  async closeBrowserSession(accountId) {
    const session = this.activeSessions.get(accountId);
    if (session) {
      try {
        await session.browser.close();
      } catch (error) {
        console.error(`Error closing browser for ${accountId}:`, error);
      }
      this.activeSessions.delete(accountId);
    }

    // Cleanup SOCKS5 bridge if exists
    try {
      await this.proxyService.stopSocks5Bridge(accountId);
    } catch (error) {
      console.error(`Error stopping SOCKS5 bridge for ${accountId}:`, error);
    }
  }

  /**
   * Stop automation
   */
  async stopAutomation() {
    this.isRunning = false;
    this.wsServer.sendLog('info', 'Stopping automation...');

    // Close all active sessions
    for (const [accountId, session] of this.activeSessions) {
      await this.closeBrowserSession(accountId);
    }

    // Stop all SOCKS5 bridges
    try {
      await this.proxyService.stopAllSocks5Bridges();
    } catch (error) {
      console.error('Error stopping SOCKS5 bridges:', error);
    }

    this.wsServer.sendLog('success', 'Automation stopped');
  }

  /**
   * Send detailed automation progress update
   */
  sendAutomationProgress(accountId, progressData) {
    this.wsServer.broadcast({
      type: 'automation_progress',
      accountId,
      data: {
        ...progressData,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Send automation stage update
   */
  sendAutomationStage(accountId, stage, data = {}) {
    this.wsServer.broadcast({
      type: 'automation_stage',
      accountId,
      stage,
      data: {
        ...data,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Send browser window info
   */
  sendBrowserWindowInfo(accountId, windowInfo) {
    this.wsServer.broadcast({
      type: 'browser_window_info',
      accountId,
      windowInfo: {
        ...windowInfo,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Get automation status with detailed info
   */
  getStatus() {
    const sessions = Array.from(this.activeSessions.entries()).map(([accountId, session]) => ({
      accountId,
      startTime: session.startTime,
      duration: Date.now() - session.startTime,
      memoryUsage: session.memoryUsage
    }));

    return {
      isRunning: this.isRunning,
      activeSessions: this.activeSessions.size,
      accounts: Array.from(this.activeSessions.keys()),
      sessions,
      windowPositions: this.windowPositions || []
    };
  }
}

module.exports = FollowInteractAutomation;
