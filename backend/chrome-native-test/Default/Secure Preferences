{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "/Applications/Google Chrome.app/Contents/Frameworks/Google Chrome Framework.framework/Versions/137.0.7151.122/Resources/web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "/Applications/Google Chrome.app/Contents/Frameworks/Google Chrome Framework.framework/Versions/137.0.7151.122/Resources/pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"browser": {"show_home_button": "CEC4550A85C9230C4D449956B3A6163B4A6BD7DD027939D2709FC29AFDF61B6B"}, "default_search_provider_data": {"template_url_data": "E31584050AB4E9E5144F70153AC275999221BBD759DF2EE36003EAC77510EB00"}, "enterprise_signin": {"policy_recovery_token": "A4A216C74C65E8BEFF6B407ADE0A31FB9BF7422F1325359169121AE702EE0C48"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "A07FCDE64342A3C184818ECAEB935C5261DAE26573D508844C4247B093B8EE03", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "48754252CE7AA1EDCA254EE0CFEE9E4F2DFD696B394C5D9758CF4BB2F0EC4753"}, "ui": {"developer_mode": "001C7D41D325562B5160F5A3A6F1AB634CA8BB5923459546EC1DDB5DF4F67ECD"}}, "google": {"services": {"account_id": "778AEDD941F7611340A3A79550D1D97EF4D2BB8663EB64F14721DAF103CAFCDE", "last_signed_in_username": "1F78863B2F3507E1A232B3C377B6C10785FEFA01D3F5E85377C392C41DAB52F1", "last_username": "27C22A471C51C0209C797F1F4281C7B8273E52E83A002BAF3F6D9D5494352DB8"}}, "homepage": "73B592D67619648E00F9498D5289B13FB5BA69449F7B392096C8B66EAEB6AE6C", "homepage_is_newtabpage": "8EDD2B32D22D95DE7A5B9EFFAFEE13D006AD87C5BDF4B1D99D59198B108292F5", "media": {"storage_id_salt": "61B6725371B08A447A65CFE00BEA5724B894CADF32FA1D0D4379EC64061A6799"}, "pinned_tabs": "D5AC5DC4026CF501650B41C5444E85885D971AA3FEDB4E8EB8A0CC3932C6243B", "prefs": {"preference_reset_time": "D881B0D8581F43AE551AF2A3E11958013DC3E3D8D283B5015CADEC8125E7E985"}, "safebrowsing": {"incidents_sent": "EE51108169D2D701CB0DBED68FB6E9219C656E88B52E38255AAB53D32857E7AB"}, "search_provider_overrides": "FE3D3B7925C0ACF57C24EDB2FA75579816FBE2EB5F9CFFA17481244BC2CC05AF", "session": {"restore_on_startup": "FDB583BB1DD0952BF2F6CA747298F35D7E00B5FFEFD20D248E668DDC686C1ACC", "startup_urls": "C3A4DE479F44565ADB68BAA05D809A11ED2F29EDD4273335733A50FBA507E529"}}, "super_mac": "FFD73FAE2974DC0B56BF071133271BC756D1CCF57F0D3D9F8EBFCC6018724F21"}}