#!/usr/bin/env node

/**
 * FINAL SOLUTION - TikTok Video Playback
 * Configuration cuối cùng đã được verify hoạt động
 */

const { chromium } = require('playwright');
const AntidetectManager = require('./src/antidetect/antidetect-manager');

async function testFinalSolution() {
  console.log('🎯 FINAL SOLUTION - TIKTOK VIDEO PLAYBACK\n');
  
  const antidetectManager = new AntidetectManager();
  let browser, page;
  
  try {
    // Load personas
    await antidetectManager.loadPersonas();
    const persona = antidetectManager.getRandomPersona();
    console.log(`🎭 Using persona: ${persona.platform}\n`);
    
    // FINAL WORKING CONFIGURATION
    console.log('🚀 Launching with FINAL working configuration...');
    
    const launchOptions = {
      headless: false,
      devtools: false,
      // Native Chrome path
      executablePath: process.platform === 'darwin' 
        ? '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
        : process.platform === 'win32'
        ? 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe'
        : '/usr/bin/google-chrome',
      ignoreDefaultArgs: [
        '--enable-automation',
        '--enable-blink-features=AutomationControlled'
      ],
      args: [
        // Minimal stealth flags
        '--disable-blink-features=AutomationControlled',
        '--exclude-switches=enable-automation',
        '--disable-automation',
        '--disable-infobars',
        '--no-default-browser-check',
        '--no-first-run',
        
        // Video optimization
        '--autoplay-policy=no-user-gesture-required',
        '--enable-accelerated-video-decode',
        '--use-gl=desktop',
        
        // Security
        '--no-sandbox',
        '--disable-setuid-sandbox',
        
        // Minimal logging
        '--disable-logging',
        '--silent',
        '--log-level=3'
      ]
    };

    const contextOptions = await antidetectManager.createTikTokContextOptions(persona);
    
    browser = await chromium.launchPersistentContext('./final-solution-profile', {
      ...launchOptions,
      ...contextOptions
    });

    // Minimal stealth script
    await browser.addInitScript(() => {
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });
      console.log('🔧 Final stealth applied');
    });
    
    page = await browser.newPage();
    console.log('✅ Final solution browser launched!\n');
    
    // Navigate to TikTok
    console.log('🌐 Navigating to TikTok...');
    await page.goto('https://www.tiktok.com', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    console.log('✅ TikTok loaded!\n');
    
    // Wait for content
    console.log('⏳ Waiting for content to load...');
    await page.waitForTimeout(8000);
    
    // Final comprehensive test
    console.log('🔍 FINAL COMPREHENSIVE TEST');
    const finalTest = await page.evaluate(() => {
      const videos = document.querySelectorAll('video');
      const bodyText = document.body.textContent || '';
      
      // Check automation detection
      const automationDetected = navigator.webdriver !== undefined;
      
      // Check for error messages
      const errorMessages = [
        "We're having trouble playing this video",
        "Please refresh and try again",
        "Video unavailable",
        "Something went wrong",
        "Error loading video"
      ];
      
      const foundErrors = errorMessages.filter(msg => 
        bodyText.toLowerCase().includes(msg.toLowerCase())
      );
      
      // Analyze first video
      let videoStatus = null;
      if (videos.length > 0) {
        const video = videos[0];
        videoStatus = {
          readyState: video.readyState,
          networkState: video.networkState,
          hasSource: !!(video.src || video.currentSrc),
          error: video.error ? video.error.message : null,
          paused: video.paused,
          duration: video.duration,
          dimensions: `${video.videoWidth}x${video.videoHeight}`
        };
      }
      
      return {
        automationDetected,
        videoCount: videos.length,
        video: videoStatus,
        errorMessages: foundErrors,
        hasErrors: foundErrors.length > 0,
        pageTitle: document.title,
        contentLength: bodyText.length
      };
    });
    
    console.log(`   Page title: ${finalTest.pageTitle}`);
    console.log(`   Content length: ${finalTest.contentLength} chars`);
    console.log(`   Automation detected: ${finalTest.automationDetected} ${!finalTest.automationDetected ? '✅' : '❌'}`);
    console.log(`   Videos found: ${finalTest.videoCount} ${finalTest.videoCount > 0 ? '✅' : '❌'}`);
    console.log(`   Error messages: ${finalTest.hasErrors} ${!finalTest.hasErrors ? '✅' : '❌'}`);
    
    if (finalTest.hasErrors) {
      console.log(`   Found errors: ${finalTest.errorMessages.join(', ')}`);
    }
    
    if (finalTest.video) {
      console.log(`\n   📹 VIDEO STATUS:`);
      console.log(`     Ready state: ${finalTest.video.readyState} ${finalTest.video.readyState >= 2 ? '✅' : finalTest.video.readyState >= 1 ? '⚠️' : '❌'}`);
      console.log(`     Network state: ${finalTest.video.networkState} ${finalTest.video.networkState <= 2 ? '✅' : '❌'}`);
      console.log(`     Has source: ${finalTest.video.hasSource} ${finalTest.video.hasSource ? '✅' : '❌'}`);
      console.log(`     Error: ${finalTest.video.error || 'none'} ${!finalTest.video.error ? '✅' : '❌'}`);
      console.log(`     Paused: ${finalTest.video.paused}`);
      console.log(`     Duration: ${finalTest.video.duration}s`);
      console.log(`     Dimensions: ${finalTest.video.dimensions}`);
    }
    
    // Try video playback
    if (finalTest.videoCount > 0) {
      console.log('\n🎬 TESTING VIDEO PLAYBACK...');
      const playbackResult = await page.evaluate(async () => {
        const video = document.querySelector('video');
        if (!video) return { success: false, error: 'No video found' };
        
        try {
          video.muted = false;
          const playPromise = video.play();
          
          if (playPromise !== undefined) {
            await playPromise;
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            return {
              success: !video.paused,
              paused: video.paused,
              currentTime: video.currentTime,
              playing: !video.paused && video.currentTime > 0
            };
          } else {
            return { success: false, error: 'Play method returned undefined' };
          }
        } catch (error) {
          return { success: false, error: error.message };
        }
      });
      
      console.log(`   Playback initiated: ${playbackResult.success} ${playbackResult.success ? '✅' : '❌'}`);
      if (playbackResult.success) {
        console.log(`   Video playing: ${playbackResult.playing} ${playbackResult.playing ? '✅' : '❌'}`);
        console.log(`   Current time: ${playbackResult.currentTime}s`);
      } else {
        console.log(`   Playback error: ${playbackResult.error}`);
      }
    }
    
    // FINAL VERDICT
    console.log('\n🏆 FINAL VERDICT');
    const isFullyWorking = !finalTest.automationDetected &&
                          finalTest.videoCount > 0 &&
                          finalTest.video?.hasSource &&
                          finalTest.video?.readyState >= 2 &&
                          finalTest.video?.networkState <= 2 &&
                          !finalTest.video?.error;
    
    const isPartiallyWorking = !finalTest.automationDetected &&
                              finalTest.videoCount > 0 &&
                              finalTest.video?.hasSource &&
                              finalTest.video?.readyState >= 1;
    
    if (isFullyWorking) {
      console.log('🎉 PERFECT SUCCESS: TikTok videos fully functional!');
      console.log('✅ No automation detection');
      console.log('✅ Videos found and fully loaded');
      console.log('✅ Video sources available');
      console.log('✅ No video errors');
      console.log('✅ Ready for production use');
    } else if (isPartiallyWorking) {
      console.log('🎯 PARTIAL SUCCESS: TikTok videos working with minor issues');
      console.log('✅ No automation detection');
      console.log('✅ Videos found and loading');
      console.log('✅ Video sources available');
      if (finalTest.hasErrors) console.log('⚠️  Some error messages present (may not affect functionality)');
      console.log('✅ Ready for testing and refinement');
    } else {
      console.log('⚠️  NEEDS IMPROVEMENT: Some issues detected');
      if (finalTest.automationDetected) console.log('❌ Automation still detected');
      if (finalTest.videoCount === 0) console.log('❌ No videos found');
      if (!finalTest.video?.hasSource) console.log('❌ No video sources');
      if (finalTest.video?.error) console.log('❌ Video errors present');
    }
    
    console.log('\n📋 IMPLEMENTATION SUMMARY:');
    console.log('1. Use Native Chrome executable path');
    console.log('2. Minimal automation flags for stealth');
    console.log('3. Video optimization flags');
    console.log('4. Minimal stealth script (only remove webdriver)');
    console.log('5. Standard TikTok context options');
    
    console.log('\n📝 Manual verification:');
    console.log('1. Check if videos are visible and loading');
    console.log('2. Try clicking play on videos');
    console.log('3. Check video playback quality');
    console.log('4. Test scrolling and navigation');
    console.log('\n⌨️  Press Ctrl+C when done testing');
    
    // Keep browser open for manual verification
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Final solution test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Handle shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Final solution test completed');
  process.exit(0);
});

testFinalSolution().catch(console.error);
