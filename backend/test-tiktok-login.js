#!/usr/bin/env node

/**
 * Test TikTok Login with Enhanced Antidetect
 * Ki<PERSON><PERSON> tra khả năng đăng nhập TikTok với hệ thống antidetect mới
 */

const { chromium } = require('playwright');
const AntidetectManager = require('./src/antidetect/antidetect-manager');

async function testTikTokLogin() {
  console.log('🧪 Testing TikTok Login with Enhanced Antidetect...\n');
  
  const antidetectManager = new AntidetectManager();
  let browser, page;
  
  try {
    // Load personas
    await antidetectManager.loadPersonas();
    const persona = antidetectManager.getRandomPersona();
    console.log(`🎭 Using persona: ${persona.platform} - ${persona.userAgent.slice(0, 60)}...`);
    console.log(`   Screen: ${persona.screen.width}x${persona.screen.height}`);
    console.log(`   Timezone: ${persona.timezone}\n`);
    
    // Create TikTok-optimized browser with persistent context (like real usage)
    const launchOptions = antidetectManager.createTikTokBrowserLaunchOptions();
    const contextOptions = await antidetectManager.createTikTokContextOptions(persona);
    
    console.log('🚀 Launching TikTok-optimized browser with persistent profile...');
    browser = await chromium.launchPersistentContext('./test-login-profile', {
      ...launchOptions,
      ...contextOptions
    });

    // Apply enhanced spoofing
    const spoofingScript = antidetectManager.createSpoofingScript(persona);
    await browser.addInitScript(spoofingScript);
    
    page = await browser.newPage();
    
    console.log('✅ Browser launched successfully!');
    
    // Navigate to TikTok login page
    console.log('🎵 Navigating to TikTok login page...');
    await page.goto('https://www.tiktok.com/login', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    console.log('✅ TikTok login page loaded!');
    
    // Wait for page to fully load
    await page.waitForTimeout(3000);
    
    // Check for automation detection on login page
    const loginPageTest = await page.evaluate(() => {
      return {
        automationBanner: document.querySelector('[data-test-id="automation-infobar"]') !== null,
        infobars: document.querySelectorAll('.infobar, [class*="automation"]').length,
        loginOptions: document.querySelectorAll('[data-e2e="channel-item"], [data-e2e="login-button"]').length,
        hasLoginForm: document.querySelector('form, [data-e2e="login-form"]') !== null,
        pageTitle: document.title,
        currentUrl: window.location.href
      };
    });
    
    console.log('\n🔍 Login Page Analysis:');
    console.log(`   Page title: ${loginPageTest.pageTitle}`);
    console.log(`   Current URL: ${loginPageTest.currentUrl}`);
    console.log(`   Automation banner: ${loginPageTest.automationBanner}`);
    console.log(`   Infobar count: ${loginPageTest.infobars}`);
    console.log(`   Login options found: ${loginPageTest.loginOptions}`);
    console.log(`   Has login form: ${loginPageTest.hasLoginForm}`);
    
    // Check for login methods
    console.log('\n🔍 Available Login Methods:');
    const loginMethods = await page.evaluate(() => {
      const methods = [];
      
      // Check for email/phone login
      if (document.querySelector('[data-e2e="email-login"], [placeholder*="email"], [placeholder*="phone"]')) {
        methods.push('Email/Phone');
      }
      
      // Check for QR code login
      if (document.querySelector('[data-e2e="qr-login"], canvas, [class*="qr"]')) {
        methods.push('QR Code');
      }
      
      // Check for social logins
      if (document.querySelector('[data-e2e="google-login"], [class*="google"]')) {
        methods.push('Google');
      }
      
      if (document.querySelector('[data-e2e="facebook-login"], [class*="facebook"]')) {
        methods.push('Facebook');
      }
      
      if (document.querySelector('[data-e2e="twitter-login"], [class*="twitter"]')) {
        methods.push('Twitter');
      }
      
      return methods;
    });
    
    console.log(`   Available methods: ${loginMethods.join(', ')}`);
    
    // Test form interaction capability
    console.log('\n🔍 Testing Form Interaction:');
    try {
      // Try to find and interact with login elements
      const emailInput = await page.locator('[data-e2e="email-login"], [placeholder*="email"], [placeholder*="phone"], input[type="email"], input[type="tel"]').first();
      
      if (await emailInput.isVisible({ timeout: 5000 })) {
        console.log('   ✅ Email/Phone input field found and accessible');
        
        // Test typing (without actually entering credentials)
        await emailInput.click();
        await page.waitForTimeout(500);
        await emailInput.fill('<EMAIL>');
        await page.waitForTimeout(500);
        await emailInput.clear();
        
        console.log('   ✅ Form interaction working normally');
      } else {
        console.log('   ⚠️  Email/Phone input not immediately visible');
      }
    } catch (error) {
      console.log(`   ⚠️  Form interaction test failed: ${error.message}`);
    }
    
    // Check for CAPTCHA or bot detection
    console.log('\n🔍 Bot Detection Check:');
    const botDetection = await page.evaluate(() => {
      return {
        hasCaptcha: document.querySelector('[data-e2e="captcha"], .captcha, [class*="captcha"]') !== null,
        hasRecaptcha: document.querySelector('.g-recaptcha, [data-sitekey]') !== null,
        hasCloudflare: document.querySelector('[data-cf-beacon]') !== null,
        suspiciousElements: document.querySelectorAll('[class*="bot"], [class*="verify"], [data-e2e*="verify"]').length
      };
    });
    
    console.log(`   CAPTCHA detected: ${botDetection.hasCaptcha}`);
    console.log(`   reCAPTCHA detected: ${botDetection.hasRecaptcha}`);
    console.log(`   Cloudflare protection: ${botDetection.hasCloudflare}`);
    console.log(`   Suspicious elements: ${botDetection.suspiciousElements}`);
    
    // Final assessment
    console.log('\n📊 Login Test Assessment:');
    const isLoginReady = !loginPageTest.automationBanner && 
                        loginMethods.length > 0 && 
                        !botDetection.hasCaptcha;
    
    if (isLoginReady) {
      console.log('🎉 SUCCESS: TikTok login appears fully functional!');
      console.log('✅ No automation detection');
      console.log('✅ Login methods available');
      console.log('✅ Form interaction working');
      console.log('✅ No immediate bot challenges');
    } else {
      console.log('⚠️  PARTIAL SUCCESS: Some issues detected');
      if (loginPageTest.automationBanner) console.log('❌ Automation banner still visible');
      if (loginMethods.length === 0) console.log('❌ No login methods found');
      if (botDetection.hasCaptcha) console.log('❌ CAPTCHA challenge present');
    }
    
    console.log('\n📝 Manual Testing Instructions:');
    console.log('1. Check that NO automation banner is visible');
    console.log('2. Try logging in with your TikTok credentials');
    console.log('3. Verify that videos play normally after login');
    console.log('4. Test following/unfollowing functionality');
    console.log('\n⌨️  Press Ctrl+C to close browser when done testing');
    
    // Keep browser open for manual testing
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n👋 Shutting down test...');
  process.exit(0);
});

// Run the test
testTikTokLogin().catch(error => {
  console.error('❌ Failed to run test:', error);
  process.exit(1);
});
