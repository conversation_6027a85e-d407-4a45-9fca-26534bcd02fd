#!/usr/bin/env node

/**
 * Final Test: Automation Banner Removal
 * Test cuối cùng để xác nhận automation banner đã bị loại bỏ hoàn toàn
 */

const { chromium } = require('playwright');
const AntidetectManager = require('./src/antidetect/antidetect-manager');

async function testFinalAutomationBanner() {
  console.log('🎯 FINAL TEST: Automation Banner Removal\n');
  console.log('🎯 Mục tiêu: Xác nhận "Chrome is being controlled by automated test software" đã biến mất\n');
  
  const antidetectManager = new AntidetectManager();
  let browser, page;
  
  try {
    // Load personas
    await antidetectManager.loadPersonas();
    const persona = antidetectManager.getRandomPersona();
    console.log(`🎭 Using persona: ${persona.platform}\n`);
    
    // Use the working method from ultimate stealth test
    console.log('🚀 Launching browser with proven stealth configuration...');
    
    const launchOptions = {
      headless: false,
      devtools: false,
      ignoreDefaultArgs: ['--enable-automation'],
      args: [
        '--disable-blink-features=AutomationControlled',
        '--exclude-switches=enable-automation',
        '--disable-automation',
        '--disable-infobars',
        '--disable-dev-shm-usage',
        '--test-type',
        '--no-default-browser-check',
        '--no-first-run',
        '--disable-default-apps',
        '--disable-extensions',
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=TranslateUI',
        '--disable-ipc-flooding-protection',
        '--disable-logging',
        '--silent',
        '--log-level=3'
      ]
    };

    const contextOptions = await antidetectManager.createTikTokContextOptions(persona);
    
    browser = await chromium.launchPersistentContext('./final-test-profile', {
      ...launchOptions,
      ...contextOptions
    });

    // Apply ultimate stealth script
    const stealthScript = antidetectManager.createUltimateStealthScript(persona);
    await browser.addInitScript(stealthScript);
    
    page = await browser.newPage();
    console.log('✅ Browser launched successfully!\n');
    
    // Test automation detection immediately
    console.log('🔍 Checking automation detection...');
    const automationTest = await page.evaluate(() => {
      return {
        webdriver: navigator.webdriver,
        webdriverType: typeof navigator.webdriver,
        hasWebdriverProp: 'webdriver' in navigator
      };
    });
    
    console.log(`   navigator.webdriver: ${automationTest.webdriver} ${automationTest.webdriver === undefined ? '✅' : '❌'}`);
    console.log(`   typeof navigator.webdriver: ${automationTest.webdriverType} ${automationTest.webdriverType === 'undefined' ? '✅' : '❌'}`);
    console.log(`   'webdriver' in navigator: ${automationTest.hasWebdriverProp} ${automationTest.hasWebdriverProp ? '✅' : '❌'}\n`);
    
    // Navigate to TikTok
    console.log('🎵 Navigating to TikTok...');
    await page.goto('https://www.tiktok.com', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    console.log('✅ TikTok loaded!\n');
    
    // Wait a bit for any automation banners to appear
    await page.waitForTimeout(3000);
    
    // Check for automation banner
    console.log('🔍 Checking for automation banner...');
    const bannerCheck = await page.evaluate(() => {
      // Check for automation banner elements
      const bannerSelectors = [
        '[data-test-id="automation-infobar"]',
        '.infobar',
        '[class*="automation"]',
        '[class*="infobar"]',
        '[data-testid*="automation"]',
        '[id*="automation"]',
        '[class*="controlled"]',
        '[data-test*="automation"]'
      ];
      
      let bannerElements = [];
      bannerSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        bannerElements = bannerElements.concat(Array.from(elements));
      });
      
      // Check for automation text in page
      const bodyText = document.body.textContent || '';
      const automationTexts = [
        'automated test software',
        'being controlled',
        'automation',
        'Chrome is being controlled'
      ];
      
      const foundTexts = automationTexts.filter(text => 
        bodyText.toLowerCase().includes(text.toLowerCase())
      );
      
      // Check for specific automation banner text
      const hasAutomationBanner = bodyText.includes('Chrome is being controlled by automated test software');
      
      return {
        bannerElements: bannerElements.length,
        bannerTexts: bannerElements.map(el => el.textContent || el.innerText).filter(t => t),
        foundAutomationTexts: foundTexts,
        hasSpecificBanner: hasAutomationBanner,
        pageTitle: document.title,
        url: window.location.href
      };
    });
    
    console.log(`   Automation banner elements found: ${bannerCheck.bannerElements} ${bannerCheck.bannerElements === 0 ? '✅' : '❌'}`);
    console.log(`   Specific automation banner: ${bannerCheck.hasSpecificBanner} ${!bannerCheck.hasSpecificBanner ? '✅' : '❌'}`);
    console.log(`   Automation texts found: ${bannerCheck.foundAutomationTexts.length} ${bannerCheck.foundAutomationTexts.length === 0 ? '✅' : '❌'}`);
    
    if (bannerCheck.bannerTexts.length > 0) {
      console.log(`   Banner texts: ${bannerCheck.bannerTexts.join(', ')}`);
    }
    
    if (bannerCheck.foundAutomationTexts.length > 0) {
      console.log(`   Found automation texts: ${bannerCheck.foundAutomationTexts.join(', ')}`);
    }
    
    console.log(`   Page title: ${bannerCheck.pageTitle}\n`);
    
    // Check video functionality
    console.log('🎬 Checking video functionality...');
    const videoCheck = await page.evaluate(() => {
      const videos = document.querySelectorAll('video');
      return {
        videoCount: videos.length,
        hasVideos: videos.length > 0
      };
    });
    
    console.log(`   Videos found: ${videoCheck.videoCount} ${videoCheck.hasVideos ? '✅' : '⚠️'}\n`);
    
    // Final result
    console.log('📊 FINAL RESULT');
    const success = automationTest.webdriver === undefined &&
                   bannerCheck.bannerElements === 0 &&
                   !bannerCheck.hasSpecificBanner &&
                   bannerCheck.foundAutomationTexts.length === 0;
    
    if (success) {
      console.log('🎉 SUCCESS: Automation banner completely removed!');
      console.log('✅ No webdriver detection');
      console.log('✅ No automation banner elements');
      console.log('✅ No automation text found');
      console.log('✅ TikTok works normally');
      console.log('\n🎯 MISSION ACCOMPLISHED: Browser appears completely normal!');
    } else {
      console.log('⚠️  PARTIAL SUCCESS: Some issues remain');
      if (automationTest.webdriver !== undefined) console.log('❌ Webdriver still detected');
      if (bannerCheck.bannerElements > 0) console.log('❌ Automation banner elements found');
      if (bannerCheck.hasSpecificBanner) console.log('❌ Specific automation banner found');
      if (bannerCheck.foundAutomationTexts.length > 0) console.log('❌ Automation texts found');
    }
    
    console.log('\n📝 Manual verification:');
    console.log('1. Look at the browser window');
    console.log('2. Check if you see "Chrome is being controlled by automated test software"');
    console.log('3. Try scrolling TikTok feed');
    console.log('4. Try playing videos');
    console.log('5. Try logging in (optional)');
    console.log('\n⌨️  Press Ctrl+C when done');
    
    // Keep browser open for manual verification
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Final test failed:', error.message);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n👋 Closing final test...');
  process.exit(0);
});

// Run the test
testFinalAutomationBanner().catch(error => {
  console.error('❌ Failed to run final test:', error);
  process.exit(1);
});
