#!/usr/bin/env node

/**
 * Ultimate Stealth Test - Loại bỏ hoàn toàn automation banner
 * Sử dụng approach mới với system Chrome và enhanced stealth
 */

const { chromium } = require('playwright');
const AntidetectManager = require('./src/antidetect/antidetect-manager');

async function testUltimateStealth() {
  console.log('🛡️ ULTIMATE STEALTH TEST - Loại bỏ hoàn toàn automation banner\n');
  
  const antidetectManager = new AntidetectManager();
  let browser, page;
  
  try {
    // Load personas
    await antidetectManager.loadPersonas();
    const persona = antidetectManager.getRandomPersona();
    console.log(`🎭 Using persona: ${persona.platform} - ${persona.userAgent.slice(0, 50)}...\n`);
    
    // Method 1: Try with createStealthBrowser
    console.log('🔧 Method 1: Enhanced Stealth Browser...');
    try {
      browser = await antidetectManager.createStealthBrowser(persona, null, './ultimate-stealth-profile');
      console.log('✅ Enhanced stealth browser launched successfully!\n');
    } catch (error) {
      console.log(`❌ Method 1 failed: ${error.message}`);
      
      // Method 2: Fallback to manual approach
      console.log('\n🔧 Method 2: Manual stealth approach...');
      
      const launchOptions = {
        headless: false,
        devtools: false,
        ignoreDefaultArgs: ['--enable-automation'],
        args: [
          '--disable-blink-features=AutomationControlled',
          '--exclude-switches=enable-automation',
          '--disable-automation',
          '--disable-infobars',
          '--disable-dev-shm-usage',
          '--test-type',
          '--no-default-browser-check',
          '--no-first-run',
          '--disable-default-apps',
          '--disable-extensions',
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--disable-features=TranslateUI',
          '--disable-ipc-flooding-protection',
          '--disable-logging',
          '--silent',
          '--log-level=3'
        ]
      };

      const contextOptions = await antidetectManager.createTikTokContextOptions(persona);
      
      browser = await chromium.launchPersistentContext('./ultimate-stealth-profile-2', {
        ...launchOptions,
        ...contextOptions
      });

      // Apply ultimate stealth script
      const stealthScript = antidetectManager.createUltimateStealthScript(persona);
      await browser.addInitScript(stealthScript);
      
      console.log('✅ Manual stealth browser launched!\n');
    }
    
    page = await browser.newPage();
    
    // Test 1: Immediate automation detection check
    console.log('🔍 Test 1: Immediate Automation Detection Check');
    const immediateTest = await page.evaluate(() => {
      return {
        webdriver: navigator.webdriver,
        webdriverInNavigator: 'webdriver' in navigator,
        webdriverType: typeof navigator.webdriver,
        automationControlled: window.chrome && window.chrome.runtime && window.chrome.runtime.onConnect,
        hasChrome: typeof window.chrome !== 'undefined'
      };
    });
    
    console.log(`   navigator.webdriver: ${immediateTest.webdriver} ${immediateTest.webdriver === undefined ? '✅' : '❌'}`);
    console.log(`   'webdriver' in navigator: ${immediateTest.webdriverInNavigator} ${immediateTest.webdriverInNavigator ? '✅' : '❌'}`);
    console.log(`   typeof navigator.webdriver: ${immediateTest.webdriverType} ${immediateTest.webdriverType === 'undefined' ? '✅' : '❌'}`);
    console.log(`   Chrome automation controlled: ${immediateTest.automationControlled} ${immediateTest.automationControlled === undefined ? '✅' : '❌'}`);
    console.log(`   Has chrome object: ${immediateTest.hasChrome} ${immediateTest.hasChrome ? '✅' : '❌'}\n`);
    
    // Test 2: Navigate to detection test page
    console.log('🔍 Test 2: Advanced Detection Test');
    await page.goto('https://bot.sannysoft.com/', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    await page.waitForTimeout(5000); // Wait for all tests to complete
    
    // Check detection results
    const detectionResults = await page.evaluate(() => {
      const results = {};

      // Check webdriver detection
      const webdriverElement = Array.from(document.querySelectorAll('td')).find(el =>
        el.textContent.includes('webdriver')
      );
      if (webdriverElement) {
        const resultElement = webdriverElement.nextElementSibling;
        results.webdriver = resultElement ? resultElement.textContent.trim() : 'unknown';
      }

      // Check automation detection
      const automationElements = Array.from(document.querySelectorAll('td')).filter(el =>
        el.textContent.includes('automation') || el.textContent.includes('controlled')
      );
      results.automationDetected = automationElements.length > 0;

      // Check for red/failed indicators
      const redElements = document.querySelectorAll('.red, [style*="color: red"], [style*="background: red"]');
      results.failedTests = redElements.length;

      // Check for green/passed indicators
      const greenElements = document.querySelectorAll('.green, [style*="color: green"], [style*="background: green"]');
      results.passedTests = greenElements.length;

      // Get page text for analysis
      results.pageText = document.body.textContent || '';

      return results;
    });
    
    console.log(`   Webdriver detection result: ${detectionResults.webdriver || 'not found'}`);
    console.log(`   Automation detected: ${detectionResults.automationDetected} ${!detectionResults.automationDetected ? '✅' : '❌'}`);
    console.log(`   Failed tests: ${detectionResults.failedTests}`);
    console.log(`   Passed tests: ${detectionResults.passedTests}\n`);
    
    // Test 3: TikTok functionality
    console.log('🔍 Test 3: TikTok Functionality Test');
    await page.goto('https://www.tiktok.com', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    await page.waitForTimeout(3000);
    
    // Check for automation banner on TikTok
    const tiktokTest = await page.evaluate(() => {
      // Check for automation banners
      const banners = document.querySelectorAll([
        '[data-test-id="automation-infobar"]',
        '.infobar',
        '[class*="automation"]',
        '[class*="infobar"]'
      ].join(', '));
      
      // Check for automation text
      const bodyText = document.body.textContent || '';
      const hasAutomationText = bodyText.includes('automated test software') || 
                               bodyText.includes('being controlled') ||
                               bodyText.includes('automation');
      
      // Check for videos
      const videos = document.querySelectorAll('video');
      
      return {
        bannerCount: banners.length,
        hasAutomationText: hasAutomationText,
        videoCount: videos.length,
        pageTitle: document.title,
        url: window.location.href
      };
    });
    
    console.log(`   Automation banners: ${tiktokTest.bannerCount} ${tiktokTest.bannerCount === 0 ? '✅' : '❌'}`);
    console.log(`   Automation text found: ${tiktokTest.hasAutomationText} ${!tiktokTest.hasAutomationText ? '✅' : '❌'}`);
    console.log(`   Videos found: ${tiktokTest.videoCount} ${tiktokTest.videoCount > 0 ? '✅' : '⚠️'}`);
    console.log(`   Page title: ${tiktokTest.pageTitle}\n`);
    
    // Final assessment
    console.log('📊 FINAL ASSESSMENT');
    const allTestsPassed = immediateTest.webdriver === undefined &&
                          immediateTest.webdriverType === 'undefined' &&
                          !detectionResults.automationDetected &&
                          tiktokTest.bannerCount === 0 &&
                          !tiktokTest.hasAutomationText;
    
    if (allTestsPassed) {
      console.log('🎉 ULTIMATE SUCCESS: All stealth tests passed!');
      console.log('✅ No webdriver detection');
      console.log('✅ No automation banners');
      console.log('✅ TikTok loads normally');
      console.log('✅ Browser appears completely normal');
    } else {
      console.log('⚠️  PARTIAL SUCCESS: Some issues remain');
      if (immediateTest.webdriver !== undefined) console.log('❌ Webdriver still detected');
      if (detectionResults.automationDetected) console.log('❌ Automation still detected');
      if (tiktokTest.bannerCount > 0) console.log('❌ Automation banners still visible');
      if (tiktokTest.hasAutomationText) console.log('❌ Automation text still present');
    }
    
    console.log('\n📝 Manual verification:');
    console.log('1. Check browser window for automation banner');
    console.log('2. Look at bot.sannysoft.com results');
    console.log('3. Test TikTok video playback');
    console.log('4. Try TikTok login if needed');
    console.log('\n⌨️  Press Ctrl+C to close browser');
    
    // Keep browser open for manual inspection
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Ultimate stealth test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n👋 Shutting down ultimate stealth test...');
  process.exit(0);
});

// Run the test
testUltimateStealth().catch(error => {
  console.error('❌ Failed to run ultimate stealth test:', error);
  process.exit(1);
});
