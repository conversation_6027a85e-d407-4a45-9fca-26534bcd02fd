#!/usr/bin/env node

/**
 * TikTok Debug Test - Kiểm tra chi tiết tại sao không có videos
 */

const { chromium } = require('playwright');

async function debugTikTok() {
  console.log('🔍 TIKTOK DEBUG TEST\n');
  
  let browser, page;
  
  try {
    const launchOptions = {
      headless: false,
      devtools: false,
      executablePath: process.platform === 'darwin' 
        ? '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
        : process.platform === 'win32'
        ? 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe'
        : '/usr/bin/google-chrome',
      ignoreDefaultArgs: [
        '--enable-automation',
        '--enable-blink-features=AutomationControlled'
      ],
      args: [
        '--disable-blink-features=AutomationControlled',
        '--exclude-switches=enable-automation',
        '--disable-automation',
        '--disable-infobars',
        '--no-default-browser-check',
        '--no-first-run',
        '--autoplay-policy=no-user-gesture-required',
        '--enable-accelerated-video-decode',
        '--use-gl=desktop',
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-logging',
        '--silent',
        '--log-level=3'
      ]
    };

    console.log('🚀 Launching debug browser...');
    browser = await chromium.launchPersistentContext('./debug-profile', launchOptions);
    
    await browser.addInitScript(() => {
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });
    });
    
    page = await browser.newPage();
    console.log('✅ Debug browser launched!\n');
    
    // Navigate and track redirects
    console.log('🌐 Navigating to TikTok...');
    const response = await page.goto('https://www.tiktok.com', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    console.log(`   Response status: ${response.status()}`);
    console.log(`   Final URL: ${page.url()}`);
    console.log(`   Page title: ${await page.title()}\n`);
    
    // Wait longer and check multiple times
    for (let i = 1; i <= 5; i++) {
      console.log(`🔍 Check ${i}/5 - Waiting ${i * 3}s...`);
      await page.waitForTimeout(i * 3000);
      
      const analysis = await page.evaluate(() => {
        const videos = document.querySelectorAll('video');
        const divs = document.querySelectorAll('div');
        const bodyText = document.body.textContent || '';
        
        // Look for video containers
        const videoContainers = document.querySelectorAll('[data-e2e="recommend-list-item"]');
        const feedItems = document.querySelectorAll('[data-e2e="feed-item"]');
        const videoElements = document.querySelectorAll('video, [data-e2e*="video"]');
        
        // Check for loading indicators
        const loadingElements = document.querySelectorAll('[data-e2e*="loading"], .loading, .spinner');
        
        // Check for error messages
        const errorMessages = [
          "We're having trouble",
          "Please refresh",
          "Video unavailable",
          "Something went wrong",
          "Error loading",
          "Couldn't load",
          "Not available"
        ];
        
        const foundErrors = errorMessages.filter(msg => 
          bodyText.toLowerCase().includes(msg.toLowerCase())
        );
        
        return {
          url: window.location.href,
          title: document.title,
          videos: videos.length,
          divs: divs.length,
          videoContainers: videoContainers.length,
          feedItems: feedItems.length,
          videoElements: videoElements.length,
          loadingElements: loadingElements.length,
          contentLength: bodyText.length,
          hasErrors: foundErrors.length > 0,
          errors: foundErrors,
          bodyPreview: bodyText.slice(0, 200) + '...'
        };
      });
      
      console.log(`     URL: ${analysis.url}`);
      console.log(`     Title: ${analysis.title}`);
      console.log(`     Videos: ${analysis.videos}`);
      console.log(`     Video containers: ${analysis.videoContainers}`);
      console.log(`     Feed items: ${analysis.feedItems}`);
      console.log(`     Video elements: ${analysis.videoElements}`);
      console.log(`     Loading elements: ${analysis.loadingElements}`);
      console.log(`     Content length: ${analysis.contentLength}`);
      console.log(`     Has errors: ${analysis.hasErrors}`);
      if (analysis.hasErrors) {
        console.log(`     Errors: ${analysis.errors.join(', ')}`);
      }
      console.log(`     Content preview: ${analysis.bodyPreview}\n`);
      
      if (analysis.videos > 0 || analysis.videoContainers > 0) {
        console.log('✅ Videos found! Breaking loop...');
        break;
      }
    }
    
    // Try scrolling to trigger video loading
    console.log('📜 Trying to scroll to trigger video loading...');
    await page.evaluate(() => {
      window.scrollTo(0, 500);
    });
    await page.waitForTimeout(3000);
    
    await page.evaluate(() => {
      window.scrollTo(0, 1000);
    });
    await page.waitForTimeout(3000);
    
    // Final check
    console.log('🔍 FINAL CHECK after scrolling...');
    const finalCheck = await page.evaluate(() => {
      const videos = document.querySelectorAll('video');
      const videoContainers = document.querySelectorAll('[data-e2e="recommend-list-item"]');
      const feedItems = document.querySelectorAll('[data-e2e="feed-item"]');
      
      let videoInfo = null;
      if (videos.length > 0) {
        const video = videos[0];
        videoInfo = {
          src: video.src || 'no-src',
          currentSrc: video.currentSrc || 'no-currentSrc',
          readyState: video.readyState,
          networkState: video.networkState,
          error: video.error ? video.error.message : null
        };
      }
      
      return {
        videos: videos.length,
        videoContainers: videoContainers.length,
        feedItems: feedItems.length,
        video: videoInfo
      };
    });
    
    console.log(`   Videos: ${finalCheck.videos} ${finalCheck.videos > 0 ? '✅' : '❌'}`);
    console.log(`   Video containers: ${finalCheck.videoContainers}`);
    console.log(`   Feed items: ${finalCheck.feedItems}`);
    
    if (finalCheck.video) {
      console.log(`   Video info:`);
      console.log(`     Ready state: ${finalCheck.video.readyState}`);
      console.log(`     Network state: ${finalCheck.video.networkState}`);
      console.log(`     Error: ${finalCheck.video.error || 'none'}`);
      if (finalCheck.video.currentSrc !== 'no-currentSrc') {
        console.log(`     Source: ${finalCheck.video.currentSrc.slice(0, 80)}...`);
      }
    }
    
    // Try different TikTok URLs
    console.log('\n🔄 Trying different TikTok URLs...');
    
    const urlsToTry = [
      'https://www.tiktok.com/foryou',
      'https://www.tiktok.com/trending',
      'https://www.tiktok.com/@tiktok'
    ];
    
    for (const url of urlsToTry) {
      console.log(`\n🌐 Trying: ${url}`);
      try {
        await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 15000 });
        await page.waitForTimeout(5000);
        
        const urlCheck = await page.evaluate(() => {
          const videos = document.querySelectorAll('video');
          return {
            url: window.location.href,
            title: document.title,
            videos: videos.length
          };
        });
        
        console.log(`   Final URL: ${urlCheck.url}`);
        console.log(`   Title: ${urlCheck.title}`);
        console.log(`   Videos: ${urlCheck.videos} ${urlCheck.videos > 0 ? '✅' : '❌'}`);
        
        if (urlCheck.videos > 0) {
          console.log('✅ Found working URL!');
          break;
        }
      } catch (error) {
        console.log(`   Error: ${error.message}`);
      }
    }
    
    console.log('\n📝 Debug summary:');
    console.log('1. Check if TikTok is redirecting to login/region page');
    console.log('2. Try different TikTok URLs');
    console.log('3. Check if videos load after scrolling');
    console.log('4. Verify browser location/language settings');
    console.log('\n⌨️  Press Ctrl+C when done debugging');
    
    // Keep browser open for manual inspection
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Debug test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Handle shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Debug test completed');
  process.exit(0);
});

debugTikTok().catch(console.error);
