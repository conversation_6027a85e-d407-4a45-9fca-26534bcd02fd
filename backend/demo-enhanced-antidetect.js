#!/usr/bin/env node

/**
 * Demo: Enhanced TikTok Antidetect System
 * Showcase của hệ thống antidetect mới - không có automation banner
 */

const { chromium } = require('playwright');
const AntidetectManager = require('./src/antidetect/antidetect-manager');

async function demoEnhancedAntidetect() {
  console.log('🎉 DEMO: Enhanced TikTok Antidetect System (2025)\n');
  console.log('🎯 Mục tiêu: Loại bỏ hoàn toàn "Chrome is being controlled by automated test software"\n');
  
  const antidetectManager = new AntidetectManager();
  let browser, page;
  
  try {
    // Load personas
    console.log('📋 Loading personas...');
    await antidetectManager.loadPersonas();
    const persona = antidetectManager.getRandomPersona();
    console.log(`✅ Loaded ${antidetectManager.personas.length} personas`);
    console.log(`🎭 Selected persona: ${persona.platform} - ${persona.userAgent.slice(0, 50)}...\n`);
    
    // Create enhanced browser
    console.log('🚀 Creating TikTok-optimized browser...');
    const launchOptions = antidetectManager.createTikTokBrowserLaunchOptions();
    const contextOptions = await antidetectManager.createTikTokContextOptions(persona);
    
    console.log('🔧 Enhanced features:');
    console.log('   ✅ Advanced automation detection bypass');
    console.log('   ✅ TikTok-specific optimizations');
    console.log('   ✅ Enhanced spoofing scripts');
    console.log('   ✅ Realistic browser fingerprinting\n');
    
    browser = await chromium.launchPersistentContext('./demo-profile', {
      ...launchOptions,
      ...contextOptions
    });

    // Apply spoofing
    const spoofingScript = antidetectManager.createSpoofingScript(persona);
    await browser.addInitScript(spoofingScript);
    
    page = await browser.newPage();
    console.log('✅ Browser launched with enhanced antidetect!\n');
    
    // Test 1: Basic automation detection
    console.log('🔍 Test 1: Basic Automation Detection');
    const basicTest = await page.evaluate(() => {
      return {
        webdriver: navigator.webdriver,
        hasWebdriverProp: 'webdriver' in navigator,
        userAgent: navigator.userAgent.slice(0, 80) + '...'
      };
    });
    
    console.log(`   navigator.webdriver: ${basicTest.webdriver} ${basicTest.webdriver === false ? '✅' : '❌'}`);
    console.log(`   'webdriver' in navigator: ${basicTest.hasWebdriverProp} ${basicTest.hasWebdriverProp ? '✅' : '❌'}`);
    console.log(`   User Agent: ${basicTest.userAgent}\n`);
    
    // Test 2: Navigate to TikTok
    console.log('🔍 Test 2: TikTok Access Test');
    console.log('   Navigating to TikTok...');
    await page.goto('https://www.tiktok.com', { 
      waitUntil: 'domcontentloaded',
      timeout: 20000 
    });
    
    await page.waitForTimeout(3000);
    console.log('   ✅ TikTok loaded successfully!');
    
    // Test 3: Automation banner check
    console.log('\n🔍 Test 3: Automation Banner Check');
    const bannerTest = await page.evaluate(() => {
      // Check for automation banners
      const automationBanners = document.querySelectorAll([
        '[data-test-id="automation-infobar"]',
        '.infobar',
        '[class*="automation"]',
        '[class*="infobar"]',
        '[data-testid*="automation"]',
        '[id*="automation"]'
      ].join(', '));
      
      // Check for text content that might indicate automation
      const bodyText = document.body.textContent || '';
      const hasAutomationText = bodyText.includes('automated test software') || 
                               bodyText.includes('being controlled') ||
                               bodyText.includes('automation');
      
      return {
        bannerElements: automationBanners.length,
        hasAutomationText: hasAutomationText,
        pageTitle: document.title,
        url: window.location.href
      };
    });
    
    console.log(`   Automation banner elements: ${bannerTest.bannerElements} ${bannerTest.bannerElements === 0 ? '✅' : '❌'}`);
    console.log(`   Automation text in page: ${bannerTest.hasAutomationText} ${!bannerTest.hasAutomationText ? '✅' : '❌'}`);
    console.log(`   Page title: ${bannerTest.pageTitle}`);
    
    // Test 4: Video functionality
    console.log('\n🔍 Test 4: Video Functionality Test');
    const videoTest = await page.evaluate(() => {
      const videos = document.querySelectorAll('video');
      return {
        videoCount: videos.length,
        hasVideos: videos.length > 0
      };
    });
    
    console.log(`   Video elements found: ${videoTest.videoCount} ${videoTest.hasVideos ? '✅' : '⚠️'}`);
    
    // Test 5: Chrome object integrity
    console.log('\n🔍 Test 5: Chrome Object Integrity');
    const chromeTest = await page.evaluate(() => {
      return {
        hasChrome: typeof window.chrome !== 'undefined',
        hasRuntime: window.chrome && typeof window.chrome.runtime !== 'undefined',
        hasLoadTimes: window.chrome && typeof window.chrome.loadTimes === 'function',
        hasCsi: window.chrome && typeof window.chrome.csi === 'function'
      };
    });
    
    console.log(`   window.chrome exists: ${chromeTest.hasChrome} ${chromeTest.hasChrome ? '✅' : '❌'}`);
    console.log(`   chrome.runtime exists: ${chromeTest.hasRuntime} ${chromeTest.hasRuntime ? '✅' : '❌'}`);
    console.log(`   chrome.loadTimes exists: ${chromeTest.hasLoadTimes} ${chromeTest.hasLoadTimes ? '✅' : '❌'}`);
    console.log(`   chrome.csi exists: ${chromeTest.hasCsi} ${chromeTest.hasCsi ? '✅' : '❌'}`);
    
    // Final assessment
    console.log('\n📊 FINAL ASSESSMENT');
    const allTestsPassed = basicTest.webdriver === false &&
                          bannerTest.bannerElements === 0 &&
                          !bannerTest.hasAutomationText &&
                          chromeTest.hasChrome;
    
    if (allTestsPassed) {
      console.log('🎉 SUCCESS: All tests passed!');
      console.log('✅ No automation detection found');
      console.log('✅ TikTok loads normally');
      console.log('✅ Browser appears completely normal');
      console.log('✅ Ready for production use');
    } else {
      console.log('⚠️  PARTIAL SUCCESS: Some tests failed');
      if (basicTest.webdriver !== false) console.log('❌ Webdriver still detected');
      if (bannerTest.bannerElements > 0) console.log('❌ Automation banners found');
      if (bannerTest.hasAutomationText) console.log('❌ Automation text found');
      if (!chromeTest.hasChrome) console.log('❌ Chrome object missing');
    }
    
    console.log('\n🎯 DEMO COMPLETE');
    console.log('📝 Manual verification:');
    console.log('   1. Look at the browser window');
    console.log('   2. Check for any automation banners');
    console.log('   3. Try scrolling TikTok feed');
    console.log('   4. Test video playback');
    console.log('   5. Try logging in (optional)');
    console.log('\n⌨️  Press Ctrl+C to close browser');
    
    // Keep browser open for manual inspection
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Demo failed:', error.message);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n👋 Closing demo...');
  process.exit(0);
});

// Run the demo
demoEnhancedAntidetect().catch(error => {
  console.error('❌ Failed to run demo:', error);
  process.exit(1);
});
