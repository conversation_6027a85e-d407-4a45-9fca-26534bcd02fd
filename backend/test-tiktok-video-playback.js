#!/usr/bin/env node

/**
 * Test TikTok Video Playback
 * <PERSON><PERSON><PERSON> tra khả năng play video TikTok với antidetect browser
 */

const { chromium } = require('playwright');
const AntidetectManager = require('./src/antidetect/antidetect-manager');

async function testTikTokVideoPlayback() {
  console.log('🎬 TESTING TIKTOK VIDEO PLAYBACK\n');
  console.log('🎯 Mục tiêu: Đảm bảo video TikTok có thể play bình thường\n');
  
  const antidetectManager = new AntidetectManager();
  let browser, page;
  
  try {
    // Load personas
    await antidetectManager.loadPersonas();
    const persona = antidetectManager.getRandomPersona();
    console.log(`🎭 Using persona: ${persona.platform}\n`);
    
    // Test Method 1: Video-optimized configuration
    console.log('🔧 Method 1: Video-Optimized Configuration...');
    try {
      const videoOptimizedOptions = antidetectManager.createTikTokVideoOptimizedLaunchOptions();
      const contextOptions = await antidetectManager.createTikTokContextOptions(persona);
      
      browser = await chromium.launchPersistentContext('./video-test-profile', {
        ...videoOptimizedOptions,
        ...contextOptions
      });

      // Apply stealth script
      const stealthScript = antidetectManager.createUltimateStealthScript(persona);
      await browser.addInitScript(stealthScript);
      
      page = await browser.newPage();
      console.log('✅ Video-optimized browser launched!\n');
      
    } catch (error) {
      console.log(`❌ Method 1 failed: ${error.message}`);
      
      // Fallback to proven stealth method
      console.log('\n🔧 Method 2: Proven Stealth Configuration...');
      
      const launchOptions = {
        headless: false,
        devtools: false,
        ignoreDefaultArgs: ['--enable-automation'],
        args: [
          // Minimal flags for stealth + video
          '--disable-blink-features=AutomationControlled',
          '--exclude-switches=enable-automation',
          '--disable-automation',
          '--disable-infobars',
          '--test-type',
          '--no-default-browser-check',
          '--no-first-run',
          '--disable-default-apps',
          '--disable-extensions',
          '--no-sandbox',
          '--disable-setuid-sandbox',
          
          // Video optimization
          '--autoplay-policy=no-user-gesture-required',
          '--enable-accelerated-video-decode',
          '--enable-gpu-rasterization',
          '--use-gl=desktop',
          '--enable-webgl',
          '--enable-webgl2',
          '--disable-software-rasterizer',
          
          // Performance
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--disable-ipc-flooding-protection',
          '--disable-logging',
          '--silent',
          '--log-level=3'
        ]
      };

      const contextOptions = await antidetectManager.createTikTokContextOptions(persona);
      
      browser = await chromium.launchPersistentContext('./video-test-profile-2', {
        ...launchOptions,
        ...contextOptions
      });

      const stealthScript = antidetectManager.createUltimateStealthScript(persona);
      await browser.addInitScript(stealthScript);
      
      page = await browser.newPage();
      console.log('✅ Fallback browser launched!\n');
    }
    
    // Test 1: Check automation detection
    console.log('🔍 Test 1: Automation Detection Check');
    const automationTest = await page.evaluate(() => {
      return {
        webdriver: navigator.webdriver,
        webdriverType: typeof navigator.webdriver
      };
    });
    
    console.log(`   navigator.webdriver: ${automationTest.webdriver} ${automationTest.webdriver === undefined ? '✅' : '❌'}`);
    console.log(`   typeof navigator.webdriver: ${automationTest.webdriverType} ${automationTest.webdriverType === 'undefined' ? '✅' : '❌'}\n`);
    
    // Test 2: Navigate to TikTok
    console.log('🔍 Test 2: Navigate to TikTok');
    await page.goto('https://www.tiktok.com', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    console.log('✅ TikTok loaded!\n');
    
    // Wait for content to load
    await page.waitForTimeout(5000);
    
    // Test 3: Check video elements
    console.log('🔍 Test 3: Video Elements Analysis');
    const videoAnalysis = await page.evaluate(() => {
      const videos = document.querySelectorAll('video');
      const videoData = Array.from(videos).map((video, index) => ({
        index: index,
        src: video.src || 'no-src',
        currentSrc: video.currentSrc || 'no-currentSrc',
        readyState: video.readyState,
        networkState: video.networkState,
        paused: video.paused,
        muted: video.muted,
        duration: video.duration,
        videoWidth: video.videoWidth,
        videoHeight: video.videoHeight,
        error: video.error ? video.error.message : null,
        canPlayType_mp4: video.canPlayType('video/mp4'),
        canPlayType_webm: video.canPlayType('video/webm')
      }));
      
      return {
        videoCount: videos.length,
        videos: videoData.slice(0, 3) // First 3 videos
      };
    });
    
    console.log(`   Videos found: ${videoAnalysis.videoCount} ${videoAnalysis.videoCount > 0 ? '✅' : '❌'}`);
    
    if (videoAnalysis.videos.length > 0) {
      videoAnalysis.videos.forEach((video, i) => {
        console.log(`\n   Video ${i + 1}:`);
        console.log(`     Ready State: ${video.readyState} ${video.readyState >= 2 ? '✅' : '⚠️'}`);
        console.log(`     Network State: ${video.networkState}`);
        console.log(`     Paused: ${video.paused}`);
        console.log(`     Duration: ${video.duration}`);
        console.log(`     Dimensions: ${video.videoWidth}x${video.videoHeight}`);
        console.log(`     Error: ${video.error || 'none'} ${!video.error ? '✅' : '❌'}`);
        console.log(`     Can play MP4: ${video.canPlayType_mp4}`);
        console.log(`     Can play WebM: ${video.canPlayType_webm}`);
        if (video.src && video.src !== 'no-src') {
          console.log(`     Source: ${video.src.slice(0, 80)}...`);
        }
      });
    }
    
    // Test 4: Try to play video
    console.log('\n🔍 Test 4: Video Playback Test');
    const playbackTest = await page.evaluate(async () => {
      const videos = document.querySelectorAll('video');
      if (videos.length === 0) {
        return { success: false, error: 'No videos found' };
      }
      
      const video = videos[0];
      
      try {
        // Try to play the video
        const playPromise = video.play();
        
        if (playPromise !== undefined) {
          await playPromise;
          
          // Wait a bit and check if it's actually playing
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          return {
            success: !video.paused,
            paused: video.paused,
            currentTime: video.currentTime,
            duration: video.duration,
            readyState: video.readyState,
            error: null
          };
        } else {
          return {
            success: false,
            error: 'Play method returned undefined'
          };
        }
      } catch (error) {
        return {
          success: false,
          error: error.message,
          paused: video.paused,
          readyState: video.readyState
        };
      }
    });
    
    console.log(`   Playback success: ${playbackTest.success} ${playbackTest.success ? '✅' : '❌'}`);
    if (playbackTest.success) {
      console.log(`   Video playing: ${!playbackTest.paused} ${!playbackTest.paused ? '✅' : '❌'}`);
      console.log(`   Current time: ${playbackTest.currentTime}s`);
      console.log(`   Duration: ${playbackTest.duration}s`);
    } else {
      console.log(`   Error: ${playbackTest.error}`);
      console.log(`   Ready state: ${playbackTest.readyState}`);
    }
    
    // Test 5: Check for error messages
    console.log('\n🔍 Test 5: Error Message Check');
    const errorCheck = await page.evaluate(() => {
      const errorMessages = [
        "We're having trouble playing this video",
        "Please refresh and try again",
        "Video unavailable",
        "This video is not available",
        "Error loading video"
      ];
      
      const bodyText = document.body.textContent || '';
      const foundErrors = errorMessages.filter(msg => 
        bodyText.includes(msg)
      );
      
      return {
        hasErrors: foundErrors.length > 0,
        errors: foundErrors
      };
    });
    
    console.log(`   Error messages found: ${errorCheck.hasErrors} ${!errorCheck.hasErrors ? '✅' : '❌'}`);
    if (errorCheck.hasErrors) {
      console.log(`   Errors: ${errorCheck.errors.join(', ')}`);
    }
    
    // Final assessment
    console.log('\n📊 FINAL ASSESSMENT');
    const videoWorking = automationTest.webdriver === undefined &&
                        videoAnalysis.videoCount > 0 &&
                        !errorCheck.hasErrors &&
                        (playbackTest.success || videoAnalysis.videos.some(v => v.readyState >= 2));
    
    if (videoWorking) {
      console.log('🎉 SUCCESS: TikTok videos working properly!');
      console.log('✅ No automation detection');
      console.log('✅ Videos found and loaded');
      console.log('✅ No error messages');
      console.log('✅ Video playback functional');
    } else {
      console.log('⚠️  ISSUES DETECTED:');
      if (automationTest.webdriver !== undefined) console.log('❌ Automation still detected');
      if (videoAnalysis.videoCount === 0) console.log('❌ No videos found');
      if (errorCheck.hasErrors) console.log('❌ Error messages present');
      if (!playbackTest.success) console.log('❌ Video playback failed');
    }
    
    console.log('\n📝 Manual verification:');
    console.log('1. Check if videos are visible on the page');
    console.log('2. Try clicking play on a video');
    console.log('3. Check if video plays smoothly');
    console.log('4. Look for any error messages');
    console.log('\n⌨️  Press Ctrl+C when done testing');
    
    // Keep browser open for manual testing
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Video playback test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n👋 Shutting down video test...');
  process.exit(0);
});

// Run the test
testTikTokVideoPlayback().catch(error => {
  console.error('❌ Failed to run video test:', error);
  process.exit(1);
});
