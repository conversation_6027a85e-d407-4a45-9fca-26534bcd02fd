#!/usr/bin/env node

/**
 * Chrome Native Test - Sử dụng Chrome thật với minimal flags
 * Approach cuối cùng để fix TikTok video playback
 */

const { chromium } = require('playwright');

async function testChromeNative() {
  console.log('🔥 CHROME NATIVE TIKTOK TEST\n');
  
  let browser, page;
  
  try {
    // Minimal configuration - chỉ những gì cần thiết
    const launchOptions = {
      headless: false,
      devtools: false,
      // Sử dụng Chrome thật
      executablePath: process.platform === 'darwin' 
        ? '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
        : process.platform === 'win32'
        ? 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe'
        : '/usr/bin/google-chrome',
      // Loại bỏ các args có thể gây conflict
      ignoreDefaultArgs: [
        '--enable-automation',
        '--enable-blink-features=AutomationControlled'
      ],
      args: [
        // Chỉ những flags tối thiểu
        '--disable-blink-features=AutomationControlled',
        '--exclude-switches=enable-automation',
        '--disable-automation',
        '--disable-infobars',
        '--no-default-browser-check',
        '--no-first-run',
        
        // Video essentials
        '--autoplay-policy=no-user-gesture-required',
        '--enable-accelerated-video-decode',
        '--use-gl=desktop',
        
        // Security minimal
        '--no-sandbox',
        '--disable-setuid-sandbox',
        
        // Logging minimal
        '--disable-logging',
        '--silent',
        '--log-level=3'
      ]
    };

    console.log('🚀 Launching native Chrome...');
    browser = await chromium.launchPersistentContext('./chrome-native-test', launchOptions);
    
    // Minimal stealth script
    await browser.addInitScript(() => {
      // Chỉ remove webdriver
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });
      
      console.log('🔧 Minimal stealth applied');
    });
    
    page = await browser.newPage();
    console.log('✅ Native Chrome launched!\n');
    
    // Test automation detection
    console.log('🔍 Automation detection check...');
    const detection = await page.evaluate(() => ({
      webdriver: navigator.webdriver,
      webdriverType: typeof navigator.webdriver
    }));
    
    console.log(`   navigator.webdriver: ${detection.webdriver} ${detection.webdriver === undefined ? '✅' : '❌'}\n`);
    
    // Navigate to TikTok
    console.log('🌐 Navigating to TikTok...');
    await page.goto('https://www.tiktok.com', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    console.log('✅ TikTok loaded!\n');
    
    // Wait for content
    console.log('⏳ Waiting for content...');
    await page.waitForTimeout(8000);
    
    // Quick analysis
    console.log('🔍 Quick video analysis...');
    const analysis = await page.evaluate(() => {
      const videos = document.querySelectorAll('video');
      const bodyText = document.body.textContent || '';
      
      const errorMessages = [
        "We're having trouble playing this video",
        "Please refresh and try again",
        "Video unavailable",
        "Something went wrong"
      ];
      
      const hasErrors = errorMessages.some(msg => 
        bodyText.toLowerCase().includes(msg.toLowerCase())
      );
      
      let videoInfo = null;
      if (videos.length > 0) {
        const video = videos[0];
        videoInfo = {
          readyState: video.readyState,
          networkState: video.networkState,
          hasSource: !!(video.src || video.currentSrc),
          error: video.error ? video.error.message : null
        };
      }
      
      return {
        videoCount: videos.length,
        video: videoInfo,
        hasErrors: hasErrors,
        title: document.title
      };
    });
    
    console.log(`   Page title: ${analysis.title}`);
    console.log(`   Videos found: ${analysis.videoCount} ${analysis.videoCount > 0 ? '✅' : '❌'}`);
    console.log(`   Error messages: ${analysis.hasErrors} ${!analysis.hasErrors ? '✅' : '❌'}`);
    
    if (analysis.video) {
      console.log(`   Video ready state: ${analysis.video.readyState} ${analysis.video.readyState >= 1 ? '✅' : '❌'}`);
      console.log(`   Video network state: ${analysis.video.networkState} ${analysis.video.networkState <= 2 ? '✅' : '❌'}`);
      console.log(`   Video has source: ${analysis.video.hasSource} ${analysis.video.hasSource ? '✅' : '❌'}`);
      console.log(`   Video error: ${analysis.video.error || 'none'} ${!analysis.video.error ? '✅' : '❌'}`);
    }
    
    // Final assessment
    console.log('\n📊 ASSESSMENT');
    const working = detection.webdriver === undefined &&
                   analysis.videoCount > 0 &&
                   !analysis.hasErrors &&
                   analysis.video?.hasSource &&
                   analysis.video?.readyState >= 1 &&
                   analysis.video?.networkState <= 2;
    
    if (working) {
      console.log('🎉 SUCCESS: Native Chrome approach working!');
    } else {
      console.log('⚠️  ISSUES: Need manual verification');
    }
    
    console.log('\n📝 Manual test:');
    console.log('1. Are videos visible on the page?');
    console.log('2. Can you click and play videos?');
    console.log('3. Do videos play smoothly?');
    console.log('4. Any error messages visible?');
    console.log('\n⌨️  Press Ctrl+C when done');
    
    // Keep open for manual testing
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Native Chrome test failed:', error.message);
    
    // Fallback to Chromium
    console.log('\n🔄 Fallback: Testing with Chromium...');
    try {
      const fallbackOptions = {
        headless: false,
        devtools: false,
        args: [
          '--disable-blink-features=AutomationControlled',
          '--exclude-switches=enable-automation',
          '--disable-automation',
          '--disable-infobars',
          '--autoplay-policy=no-user-gesture-required',
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-logging',
          '--silent'
        ]
      };

      browser = await chromium.launchPersistentContext('./chromium-fallback-test', fallbackOptions);
      
      await browser.addInitScript(() => {
        Object.defineProperty(navigator, 'webdriver', {
          get: () => undefined,
        });
      });
      
      page = await browser.newPage();
      console.log('✅ Chromium fallback launched!');
      
      await page.goto('https://www.tiktok.com', { 
        waitUntil: 'domcontentloaded',
        timeout: 30000 
      });
      
      console.log('✅ TikTok loaded with Chromium fallback!');
      console.log('\n📝 Manual test with Chromium fallback');
      console.log('⌨️  Press Ctrl+C when done');
      
      await new Promise(() => {});
      
    } catch (fallbackError) {
      console.error('❌ Chromium fallback also failed:', fallbackError.message);
    }
  }
}

// Handle shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down...');
  process.exit(0);
});

testChromeNative().catch(console.error);
