#!/usr/bin/env node

/**
 * Simple TikTok Video Test
 * Test với configuration tối ưu nhất cho video playback
 */

const { chromium } = require('playwright');

async function testSimpleVideo() {
  console.log('🎬 SIMPLE TIKTOK VIDEO TEST\n');
  
  let browser, page;
  
  try {
    // Configuration tối ưu cho video
    const launchOptions = {
      headless: false,
      devtools: false,
      ignoreDefaultArgs: [
        '--enable-automation',
        '--enable-blink-features=AutomationControlled'
      ],
      args: [
        // Stealth minimal
        '--disable-blink-features=AutomationControlled',
        '--exclude-switches=enable-automation',
        '--disable-automation',
        '--disable-infobars',
        '--test-type',
        '--no-default-browser-check',
        '--no-first-run',
        
        // CRITICAL: Allow media loading
        '--allow-running-insecure-content',
        '--disable-web-security',
        '--disable-site-isolation-trials',
        '--disable-features=VizDisplayCompositor',
        '--disable-features=BlockInsecurePrivateNetworkRequests',
        
        // Video optimization
        '--autoplay-policy=no-user-gesture-required',
        '--enable-accelerated-video-decode',
        '--enable-gpu-rasterization',
        '--use-gl=desktop',
        '--enable-webgl',
        '--enable-webgl2',
        '--disable-software-rasterizer',
        
        // Performance
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-background-media-suspend',
        
        // Minimal flags
        '--disable-extensions',
        '--disable-sync',
        '--disable-logging',
        '--silent',
        '--log-level=3'
      ]
    };

    console.log('🚀 Launching browser with video-optimized config...');
    browser = await chromium.launchPersistentContext('./simple-video-test', launchOptions);
    
    // Add stealth script
    await browser.addInitScript(() => {
      // Remove webdriver property
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });
      
      // Override plugins
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5],
      });
      
      // Override languages
      Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en'],
      });
      
      // Override permissions
      const originalQuery = window.navigator.permissions.query;
      window.navigator.permissions.query = (parameters) => (
        parameters.name === 'notifications' ?
          Promise.resolve({ state: Notification.permission }) :
          originalQuery(parameters)
      );
    });
    
    page = await browser.newPage();
    console.log('✅ Browser launched!\n');
    
    // Test automation detection
    console.log('🔍 Checking automation detection...');
    const automationTest = await page.evaluate(() => {
      return {
        webdriver: navigator.webdriver,
        webdriverType: typeof navigator.webdriver,
        plugins: navigator.plugins.length,
        languages: navigator.languages.length
      };
    });
    
    console.log(`   navigator.webdriver: ${automationTest.webdriver} ${automationTest.webdriver === undefined ? '✅' : '❌'}`);
    console.log(`   plugins: ${automationTest.plugins} ${automationTest.plugins > 0 ? '✅' : '❌'}`);
    console.log(`   languages: ${automationTest.languages} ${automationTest.languages > 0 ? '✅' : '❌'}\n`);
    
    // Navigate to TikTok
    console.log('🌐 Navigating to TikTok...');
    await page.goto('https://www.tiktok.com', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    console.log('✅ TikTok loaded!\n');
    
    // Wait for content
    console.log('⏳ Waiting for content to load...');
    await page.waitForTimeout(8000);
    
    // Check for videos
    console.log('🔍 Analyzing video elements...');
    const videoAnalysis = await page.evaluate(() => {
      const videos = document.querySelectorAll('video');
      console.log('Found videos:', videos.length);
      
      if (videos.length === 0) {
        return { videoCount: 0, videos: [] };
      }
      
      const videoData = Array.from(videos).map((video, index) => {
        console.log(`Video ${index}:`, {
          src: video.src,
          currentSrc: video.currentSrc,
          readyState: video.readyState,
          networkState: video.networkState,
          error: video.error
        });
        
        return {
          index: index,
          src: video.src || 'no-src',
          currentSrc: video.currentSrc || 'no-currentSrc',
          readyState: video.readyState,
          networkState: video.networkState,
          paused: video.paused,
          muted: video.muted,
          duration: video.duration,
          videoWidth: video.videoWidth,
          videoHeight: video.videoHeight,
          error: video.error ? video.error.message : null,
          canPlayType_mp4: video.canPlayType('video/mp4'),
          canPlayType_webm: video.canPlayType('video/webm'),
          hasSource: video.src && video.src.length > 0
        };
      });
      
      return {
        videoCount: videos.length,
        videos: videoData.slice(0, 2)
      };
    });
    
    console.log(`   Videos found: ${videoAnalysis.videoCount} ${videoAnalysis.videoCount > 0 ? '✅' : '❌'}`);
    
    if (videoAnalysis.videos.length > 0) {
      videoAnalysis.videos.forEach((video, i) => {
        console.log(`\n   Video ${i + 1}:`);
        console.log(`     Has source: ${video.hasSource} ${video.hasSource ? '✅' : '❌'}`);
        console.log(`     Ready State: ${video.readyState} ${video.readyState >= 2 ? '✅' : video.readyState >= 1 ? '⚠️' : '❌'}`);
        console.log(`     Network State: ${video.networkState} ${video.networkState <= 2 ? '✅' : '❌'}`);
        console.log(`     Error: ${video.error || 'none'} ${!video.error ? '✅' : '❌'}`);
        console.log(`     Dimensions: ${video.videoWidth}x${video.videoHeight}`);
        console.log(`     Can play MP4: ${video.canPlayType_mp4}`);
        
        if (video.src && video.src !== 'no-src') {
          console.log(`     Source: ${video.src.slice(0, 60)}...`);
        }
        if (video.currentSrc && video.currentSrc !== 'no-currentSrc') {
          console.log(`     Current Source: ${video.currentSrc.slice(0, 60)}...`);
        }
      });
    }
    
    // Check for error messages
    console.log('\n🔍 Checking for error messages...');
    const errorCheck = await page.evaluate(() => {
      const bodyText = document.body.textContent || '';
      const errorMessages = [
        "We're having trouble playing this video",
        "Please refresh and try again",
        "Video unavailable",
        "This video is not available",
        "Error loading video",
        "Something went wrong"
      ];
      
      const foundErrors = errorMessages.filter(msg => 
        bodyText.toLowerCase().includes(msg.toLowerCase())
      );
      
      return {
        hasErrors: foundErrors.length > 0,
        errors: foundErrors,
        bodyTextLength: bodyText.length
      };
    });
    
    console.log(`   Error messages: ${errorCheck.hasErrors} ${!errorCheck.hasErrors ? '✅' : '❌'}`);
    if (errorCheck.hasErrors) {
      console.log(`   Found errors: ${errorCheck.errors.join(', ')}`);
    }
    console.log(`   Page content length: ${errorCheck.bodyTextLength} chars`);
    
    // Try to play first video
    if (videoAnalysis.videoCount > 0) {
      console.log('\n🎬 Attempting video playback...');
      const playResult = await page.evaluate(async () => {
        const video = document.querySelector('video');
        if (!video) return { success: false, error: 'No video found' };
        
        try {
          // Unmute first
          video.muted = false;
          
          // Try to play
          const playPromise = video.play();
          
          if (playPromise !== undefined) {
            await playPromise;
            
            // Wait and check
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            return {
              success: !video.paused,
              paused: video.paused,
              currentTime: video.currentTime,
              duration: video.duration,
              readyState: video.readyState,
              networkState: video.networkState,
              error: null
            };
          } else {
            return { success: false, error: 'Play method returned undefined' };
          }
        } catch (error) {
          return {
            success: false,
            error: error.message,
            paused: video.paused,
            readyState: video.readyState,
            networkState: video.networkState
          };
        }
      });
      
      console.log(`   Playback success: ${playResult.success} ${playResult.success ? '✅' : '❌'}`);
      if (playResult.success) {
        console.log(`   Video playing: ${!playResult.paused} ${!playResult.paused ? '✅' : '❌'}`);
        console.log(`   Current time: ${playResult.currentTime}s`);
        console.log(`   Duration: ${playResult.duration}s`);
      } else {
        console.log(`   Error: ${playResult.error}`);
        console.log(`   Ready state: ${playResult.readyState}`);
        console.log(`   Network state: ${playResult.networkState}`);
      }
    }
    
    // Final assessment
    console.log('\n📊 FINAL ASSESSMENT');
    const isWorking = automationTest.webdriver === undefined &&
                     videoAnalysis.videoCount > 0 &&
                     !errorCheck.hasErrors &&
                     videoAnalysis.videos.some(v => v.hasSource && v.readyState >= 1);
    
    if (isWorking) {
      console.log('🎉 SUCCESS: TikTok video system is working!');
    } else {
      console.log('⚠️  ISSUES DETECTED - Manual verification needed');
    }
    
    console.log('\n📝 Manual verification steps:');
    console.log('1. Look at the TikTok page - are videos visible?');
    console.log('2. Try clicking on a video to play it');
    console.log('3. Check if videos play smoothly');
    console.log('4. Look for any error messages on the page');
    console.log('\n⌨️  Press Ctrl+C when done testing');
    
    // Keep browser open
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Handle shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down...');
  process.exit(0);
});

testSimpleVideo().catch(console.error);
