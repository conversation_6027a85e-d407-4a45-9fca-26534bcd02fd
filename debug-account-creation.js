#!/usr/bin/env node

/**
 * Debug script to check account creation status issue
 */

const DatabaseManager = require('./backend/src/database/manager');

async function debugAccountCreation() {
  console.log('🔍 Debugging account creation status issue...\n');

  const db = new DatabaseManager();
  await db.initialize();

  // Check existing accounts
  console.log('📋 Existing accounts in database:');
  const existingAccounts = await db.getAccounts();
  existingAccounts.forEach((acc, index) => {
    console.log(`${index + 1}. ID: ${acc.id.substring(0, 8)}... | Status: ${acc.status} | Proxy: ${acc.proxy.type} | Google: ${acc.useGoogleLogin}`);
  });

  // Create a test account directly through database
  console.log('\n🧪 Creating test account directly through database...');
  
  const testAccount = {
    id: 'test-debug-' + Date.now(),
    username: null,
    password: null,
    useGoogleLogin: true,
    status: 'not_logged_in',
    proxy: {
      type: 'No proxy',
      host: null,
      port: null,
      username: null,
      password: null,
      country: null,
      city: null,
      isActive: true
    },
    ipChecker: 'ip-api',
    persona: null,
    stats: {
      followsToday: 0,
      followsThisSession: 0,
      lastActivity: null
    }
  };

  console.log('📝 Test account data:');
  console.log(`   ID: ${testAccount.id}`);
  console.log(`   Status: ${testAccount.status}`);
  console.log(`   Proxy Type: ${testAccount.proxy.type}`);
  console.log(`   Google Login: ${testAccount.useGoogleLogin}`);

  // Add to database
  await db.addAccount(testAccount);
  console.log('✅ Account added to database');

  // Immediately check the account
  const retrievedAccount = await db.getAccountById(testAccount.id);
  console.log('\n📊 Retrieved account from database:');
  console.log(`   ID: ${retrievedAccount.id}`);
  console.log(`   Status: ${retrievedAccount.status}`);
  console.log(`   Proxy Type: ${retrievedAccount.proxy.type}`);
  console.log(`   Google Login: ${retrievedAccount.useGoogleLogin}`);

  if (retrievedAccount.status === 'not_logged_in') {
    console.log('✅ Status is correct: not_logged_in');
  } else {
    console.log(`❌ Status is wrong: ${retrievedAccount.status} (expected: not_logged_in)`);
  }

  // Clean up
  await db.deleteAccount(testAccount.id);
  console.log('\n🗑️  Test account deleted');

  console.log('\n🎯 Conclusion:');
  if (retrievedAccount.status === 'not_logged_in') {
    console.log('✅ Database operations are working correctly');
    console.log('❓ The issue might be in the AccountController or WebSocket handling');
  } else {
    console.log('❌ There is an issue with database operations');
  }
}

debugAccountCreation().catch(console.error);
